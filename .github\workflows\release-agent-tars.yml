name: Release Agent-TARS app
on:
  workflow_dispatch:

env:
  CI: true
  NODE_OPTIONS: --max-old-space-size=8192
  HUSKY: 0

permissions:
  id-token: write
  contents: write
  attestations: write

jobs:
  publish_on_mac:
    strategy:
      fail-fast: false
      matrix:
        os: [macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Show operating system info
        run: |
          echo "Operating System:"
          uname -a
      - name: Install pnpm
        run: npm install -g pnpm@9
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
          cache: 'pnpm'
      - name: Install Python setuptools
        run: brew install python-setuptools
      - name: Install appdmg
        run: npm install -g appdmg
      - name: Install the Apple certificate and provisioning profile
        env:
          BUILD_CERTIFICATE_BASE64: ${{ secrets.BUILD_CERTIFICATE_BASE64 }}
          P12_PASSWORD: ${{ secrets.P12_PASSWORD }}
          BUILD_PROVISION_PROFILE_BASE64: ${{ secrets.BUILD_PROVISION_PROFILE_BASE64 }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: |
          # create variables
          CERTIFICATE_PATH=$RUNNER_TEMP/build_certificate.p12
          PP_PATH=$RUNNER_TEMP/build_pp.mobileprovision
          KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db

          # import certificate and provisioning profile from secrets
          echo -n "$BUILD_CERTIFICATE_BASE64" | base64 --decode -o $CERTIFICATE_PATH
          echo -n "$BUILD_PROVISION_PROFILE_BASE64" | base64 --decode -o $PP_PATH

          # create temporary keychain
          security create-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH

          # import certificate to keychain
          security import $CERTIFICATE_PATH -P "$P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          security set-key-partition-list -S apple-tool:,apple: -k "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security list-keychain -d user -s $KEYCHAIN_PATH

          # apply provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles
      - name: install dependencies
        run: pnpm install
      - name: publish Mac Universal
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          KEYCHAIN_PATH: ${{ runner.temp }}/app-signing.keychain-db
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          cd apps/agent-tars && pnpm run publish:mac

  # publish_on_win:
  #   runs-on: windows-latest
  #   steps:
  #     - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
  #     - name: Show operating system info
  #       run: |
  #         echo "Operating System:"
  #         cmd /c ver
  #     - name: Install pnpm
  #       run: npm install -g pnpm@9
  #     - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
  #       with:
  #         node-version: 20
  #         cache: 'pnpm'
  #     - name: install dependencies
  #       run: pnpm install
  #     - name: publish
  #       env:
  #         GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  #       run: cd apps/agent-tars && pnpm run publish:win32
