# 🚀 NextAI MVP - 统一智能体平台

轻量级智能Agent系统，展示Web端高阶Agent助手的核心功能

## 📋 项目概述

这是一个MVP（最小可行产品）实现，展示了如何将LangGraph CodeAct与Daytona Docker沙盒集成，创建一个智能的代码执行和任务处理系统。

### 🎯 核心特性

- **🧠 智能任务路由** - 自动选择CodeAct Python执行或Docker沙盒环境
- **⚡ CodeAct集成** - 基于LangGraph的智能代码生成和执行
- **🐳 Docker沙盒** - 安全的隔离执行环境
- **📊 实时监控** - WebSocket实时事件推送和可视化
- **🔄 事件回放** - 完整的执行历史记录和分析

### 🏗️ 架构设计

```
用户输入 → 任务路由器 → 执行引擎 → 结果输出
                ↓
        [CodeAct Python] 或 [Docker沙盒]
                ↓
        事件管理器 → 实时可视化
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 进入后端目录
cd nextai_backend

# 安装Python依赖
pip install -r requirements.txt

# 设置环境变量（可选）
cp .env.example .env
# 编辑 .env 文件，添加API密钥（演示模式不需要）
```

### 2. 配置环境变量

在 `backend/.env` 文件中配置：

```env
# Anthropic API (用于CodeAct)
ANTHROPIC_API_KEY=your_anthropic_api_key

# Daytona 配置 (如果使用真实沙盒)
DAYTONA_API_KEY=your_daytona_api_key
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us

# 其他配置
ENVIRONMENT=development
LOG_LEVEL=INFO
```

### 3. 启动服务

```bash
# 方法1：使用启动脚本（推荐）
python start_mvp.py

# 方法2：手动启动
cd nextai_backend
python main.py

# 服务将在 http://localhost:8000 启动
```

### 4. 测试界面

打开浏览器访问：`nextai_frontend/index.html`

或者直接用浏览器打开文件：`file:///path/to/nextai_frontend/index.html`

## 🧪 测试用例

### 测试用例1：数据分析任务（CodeAct路径）

**输入：**
```
创建一个包含销售数据的DataFrame，包含日期、产品、销售额三列，然后分析月度销售趋势并生成图表
```

**预期结果：**
- 任务路由到CodeAct Python环境
- 生成pandas代码创建数据
- 创建matplotlib图表
- 返回分析结果和图表文件

### 测试用例2：API调用任务（CodeAct路径）

**输入：**
```
调用一个公开的API获取天气数据，然后解析JSON响应并提取温度信息
```

**预期结果：**
- 使用requests库调用API
- 解析JSON响应
- 提取和展示温度数据

### 测试用例3：文件处理任务（CodeAct路径）

**输入：**
```
创建一个包含用户信息的CSV文件，然后读取并统计用户年龄分布
```

**预期结果：**
- 创建CSV文件
- 使用pandas读取和分析
- 生成年龄分布统计

### 测试用例4：复杂系统任务（Docker路径）

**输入：**
```
安装selenium并爬取网页数据
```

**预期结果：**
- 任务路由到Docker沙盒
- 安装selenium依赖
- 执行网页爬取脚本

## 📊 API接口

### 核心接口

- `POST /api/execute` - 执行任务
- `POST /api/analyze-intent` - 分析用户意图
- `GET /api/sessions/{session_id}/events` - 获取会话事件
- `GET /api/sessions/{session_id}/timeline` - 获取执行时间线
- `GET /api/stats` - 获取系统统计
- `WS /ws/{session_id}` - WebSocket实时事件

### 测试接口

- `POST /api/test/codeact` - 测试CodeAct功能
- `GET /health` - 健康检查

## 🔧 核心组件

### 1. 统一执行引擎 (`execution_engine.py`)

负责协调CodeAct和Docker沙盒的执行，提供统一的任务处理接口。

### 2. 任务路由器 (`task_router.py`)

智能分析用户意图，决定使用哪种执行环境：
- **CodeAct Python**: 数据分析、API调用、文件处理、计算任务
- **Docker沙盒**: 浏览器自动化、系统操作、环境搭建、多语言任务

### 3. Daytona评估器 (`daytona_evaluator.py`)

为CodeAct提供Docker沙盒执行能力，支持Python代码和Shell命令执行。

### 4. 事件管理器 (`event_manager.py`)

记录和管理所有执行事件，支持实时推送和历史回放。

### 5. Python工具集 (`python_tools.py`)

为CodeAct提供丰富的Python工具函数，包括文件操作、HTTP请求、数据分析等。

## 📈 验证标准

### 功能验证
- ✅ CodeAct能正确生成和执行Python代码
- ✅ 任务路由器能准确选择执行环境
- ✅ Docker沙盒能安全执行复杂任务
- ✅ 事件系统能完整记录执行过程

### 性能验证
- ✅ 平均响应时间 < 5秒
- ✅ CodeAct执行成功率 > 95%
- ✅ 任务路由准确率 > 90%

### 用户体验验证
- ✅ 界面响应流畅
- ✅ 错误信息清晰
- ✅ 实时反馈及时

## 🐛 故障排除

### 常见问题

1. **CodeAct执行失败**
   - 检查Anthropic API密钥是否正确
   - 确认网络连接正常
   - 查看后端日志获取详细错误信息

2. **Docker沙盒连接失败**
   - 检查Daytona配置是否正确
   - 确认沙盒服务状态
   - 验证API密钥权限

3. **WebSocket连接失败**
   - 确认后端服务正在运行
   - 检查防火墙设置
   - 验证端口8000是否可访问

### 日志查看

```bash
# 查看后端日志
cd backend
python main.py

# 日志会输出到控制台
```

## 🔄 下一步计划

### 短期目标（1-2周）
- [ ] 完善错误处理机制
- [ ] 添加更多测试用例
- [ ] 优化性能和稳定性
- [ ] 完善文档和部署指南

### 中期目标（1个月）
- [ ] 集成真实的Daytona沙盒
- [ ] 添加Canvas Panel可视化
- [ ] 实现事件回放功能
- [ ] 添加用户认证系统

### 长期目标（3个月）
- [ ] 完整的前端界面
- [ ] 多用户支持
- [ ] 插件系统
- [ ] 云端部署

## 📞 支持

如果遇到问题或有建议，请：

1. 查看本文档的故障排除部分
2. 检查GitHub Issues
3. 联系开发团队

---

**注意：** 这是一个MVP版本，主要用于概念验证和核心功能演示。生产环境使用需要进一步的安全加固和性能优化。
