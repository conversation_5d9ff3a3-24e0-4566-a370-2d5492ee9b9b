/**
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */

// 平台特定代码
let permissions;
let hasAccessibilityPermission = false;

// 只在macOS上导入mac权限模块
if (process.platform === 'darwin') {
  try {
    // 动态导入，避免Windows上报错
    permissions = require('@computer-use/node-mac-permissions');
  } catch (error) {
    console.warn('Failed to load @computer-use/node-mac-permissions:', error);
  }
}

const wrapWithWarning =
  (message, nativeFunction) =>
  (...args) => {
    console.warn(message);
    return nativeFunction(...args);
  };

// 针对不同平台的权限请求
const askForAccessibility = (nativeFunction, functionName) => {
  if (process.platform === 'darwin' && permissions) {
    const accessibilityStatus = permissions.getAuthStatus('accessibility');
    console.info('[accessibilityStatus]', accessibilityStatus);

    if (accessibilityStatus === 'authorized') {
      hasAccessibilityPermission = true;
      return nativeFunction;
    } else if (
      accessibilityStatus === 'not determined' ||
      accessibilityStatus === 'denied'
    ) {
      hasAccessibilityPermission = false;
      permissions.askForAccessibilityAccess();
      return wrapWithWarning(
        `##### WARNING! The application running this script tries to access accessibility features to execute ${functionName}! Please grant requested access for further information. #####`,
        nativeFunction,
      );
    }
  } else if (process.platform === 'win32') {
    // Windows平台默认不需要特殊权限请求
    hasAccessibilityPermission = true;
    return nativeFunction;
  }

  return nativeFunction;
};

export const ensurePermissions = (): {
  accessibility: boolean;
} => {
  if (process.env.CI === 'e2e') {
    return {
      accessibility: true,
    };
  }

  if (process.platform === 'darwin') {
    askForAccessibility(() => {}, 'execute accessibility');
  } else {
    // 在Windows上，我们假设已经有权限
    hasAccessibilityPermission = true;
  }

  console.info('hasAccessibilityPermission', hasAccessibilityPermission);

  return {
    accessibility: hasAccessibilityPermission,
  };
};
