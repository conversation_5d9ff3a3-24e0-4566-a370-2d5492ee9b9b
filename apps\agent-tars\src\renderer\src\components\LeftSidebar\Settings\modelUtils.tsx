import {
  SiGooglegemini,
  SiOpenai,
  SiAnthropic,
  SiMicrosoftazure,
} from 'react-icons/si';
import { AiFillApi } from 'react-icons/ai';
import MistralIcon from '@renderer/assets/Mistral';
import { ModelProvider } from '@agent-infra/shared';

export function getProviderLogo(provider: ModelProvider) {
  switch (provider) {
    case ModelProvider.OPENAI:
      return <SiOpenai size={18} />;
    case ModelProvider.ANTHROPIC:
      return <SiAnthropic size={18} />;
    case ModelProvider.GEMINI:
      return <SiGooglegemini size={18} />;
    case ModelProvider.MISTRAL:
      return <MistralIcon />;
    case ModelProvider.AZURE_OPENAI:
      return <SiMicrosoftazure size={18} />;
    case ModelProvider.DEEPSEEK:
      return <AiFillApi size={18} />;
    default:
      return <AiFillApi size={18} />;
  }
}

export function getModelOptions(provider: ModelProvider) {
  switch (provider) {
    case ModelProvider.OPENAI:
      return [
        { value: 'gpt-4o', label: 'GPT-4o' },
        { value: 'gpt-4o-mini', label: 'GPT-4o Mini' },
        { value: 'gpt-4-0613', label: 'GPT-4 (0613)' },
        { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
        { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
        { value: 'gpt-3.5-turbo-1106', label: 'GPT-3.5 Turbo (1106)' },
      ];
    case ModelProvider.ANTHROPIC:
      return [
        { value: 'claude-3-7-sonnet-latest', label: 'Claude 3.7 Sonnet' },
        { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet' },
        { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus' },
        { value: 'claude-3-sonnet-20240229', label: 'Claude 3 Sonnet' },
        { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku' },
        { value: 'claude-2.0', label: 'Claude 2' },
        { value: 'claude-2.1', label: 'Claude 2.1' },
        { value: 'claude-instant-1.2', label: 'Claude Instant 1.2' },
      ];
    case ModelProvider.GEMINI:
      return [
        { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash' },
        { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro' },
        { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash' },
      ];
    case ModelProvider.MISTRAL:
      return [
        { value: 'mistral-large-latest', label: 'Mistral Large' },
        { value: 'mistral-medium-latest', label: 'Mistral Medium' },
        { value: 'mistral-small-latest', label: 'Mistral Small' },
      ];
    case ModelProvider.DEEPSEEK:
      return [
        { value: 'deepseek-chat', label: 'Deepseek Chat (官方版)' },
        { value: 'deepseek-reasoner', label: 'Deepseek Reasoner (官方版)' },
        {
          value: 'deepseek-v3-huoshan',
          label: 'Deepseek V3 (火山引擎部署版本)',
        },
        {
          value: 'deepseek-r1-huoshan',
          label: 'Deepseek R1 (火山引擎部署版本)',
        },
        { value: 'deepseek-v3-baidu', label: 'Deepseek V3 (百度云部署版本)' },
        { value: 'deepseek-r1-baidu', label: 'Deepseek R1 (百度云部署版本)' },
        {
          value: 'deepseek-r1-multipath',
          label: 'Deepseek R1 (多渠道负载均衡版)',
        },
        { value: 'deepseek-v3-aliyun', label: 'Deepseek V3 (阿里云部署版本)' },
        { value: 'deepseek-r1-aliyun', label: 'Deepseek R1 (阿里云部署版本)' },
        { value: 'deepseek-r1-302', label: 'Deepseek R1 (302部署版本)' },
        { value: 'deepseek-v3-302', label: 'Deepseek V3 (302部署版本)' },
      ];
    case ModelProvider.AZURE_OPENAI:
      return [];
    default:
      return [];
  }
}
