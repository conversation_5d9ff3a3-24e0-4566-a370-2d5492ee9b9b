.sidebar {
  position: fixed;
  top: 16px;
  left: 0;
  height: 100vh;
  background: var(--ai-color-bg-secondary);
  box-shadow: var(--shadow-md);
  transition:
    width var(--transition-smooth),
    box-shadow var(--transition-smooth);
  overflow: hidden;
  border-right: 1px solid var(--ai-color-border);
  display: flex;
  flex-direction: column;
  z-index: 10;

  &.expanded {
    width: 260px;
  }

  &.collapsed {
    width: 56px;
  }
}

.topbar {
  flex-shrink: 0;
  padding: 8px 12px 0;
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;

  &.collapsed {
    margin-top: 0;
    padding: 20px 0 0;
    justify-content: center;
    flex-direction: column;
    height: auto;
    gap: 12px;
  }
}

.title {
  margin: 0;
  margin-left: 10px;
  font-size: 16px;
  font-weight: 600;
  color: var(--ai-color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  animation: fadeIn 0.3s ease;
}

.toggleButton {
  width: 32px;
  height: 32px;
  border: 1px solid var(--ai-color-border);
  border-radius: var(--ai-radius-md);
  background: var(--ai-color-bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform var(--transition-normal);
  color: var(--ai-color-text-tertiary);
  z-index: 10;

  &:hover {
    background: var(--ai-color-bg-hover);
    color: var(--ai-color-text-primary);
    border-color: var(--ai-color-border-hover);
  }

  &:active {
    transform: scale(0.95);
  }
}

.messageList {
  flex: 1;
  padding: 8px;
  padding-top: 0;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--ai-color-bg-quaternary);
    border-radius: var(--radius-sm);

    &:hover {
      background: var(--ai-color-text-quaternary);
    }
  }

  &.collapsed {
    display: none;
  }
}

.messageItem {
  padding: 6px 12px;
  margin: 0 4px 4px;
  flex-shrink: 0;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: transform var(--transition-normal);
  font-size: 14px;
  color: var(--ai-color-text-primary);
  background: var(--ai-color-bg-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 36px;
  padding: 0 12px;
  box-sizing: border-box;

  &:hover {
    background: var(--ai-color-bg-hover);
    color: var(--ai-color-text-primary);
  }

  &:active {
    background: var(--ai-color-bg-active);
  }

  &.active {
    background: var(--ai-color-primary-bg);
    color: var(--ai-color-primary);
    height: 36px;
    font-weight: 500;
  }

  .moreButton {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover {
    .moreButton {
      opacity: 0.7;

      &:hover {
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
}

.controls {
  display: flex;
  gap: 8px;
  align-items: center;

  &.controlsCollapsed {
    flex-direction: column;
  }
}

.messageTitle {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.messageItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.deleteButton {
  opacity: 0;
  background: none;
  border: none;
  padding: 6px;
  color: var(--ai-color-danger);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);

  &:hover {
    background: var(--ai-color-danger-bg);
    color: var(--ai-color-danger-hover);
  }
}

.messageItem:hover .deleteButton {
  opacity: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.editContainer {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.editInput {
  flex: 1;
  background: var(--ai-color-bg-primary);
  border: 1px solid var(--ai-color-border);
  border-radius: var(--radius-sm);
  padding: 2px 8px;
  font-size: 14px;
  font-weight: normal;
  color: var(--ai-color-text-primary);
  height: 24px;

  &:focus {
    outline: none;
    border-color: var(--ai-color-primary);
  }
}

.actionButtons {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity var(--transition-normal);
  height: 100%;
  display: flex;
  align-items: center;
}

.messageItem:hover .actionButtons {
  opacity: 1;
}

.editButton {
  opacity: 0;
  border: none;
  padding: 6px;
  color: var(--ai-color-text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;

  &:hover {
    color: var(--ai-color-text-primary);
  }

  &:active {
    background: var(--ai-color-button-active);
  }
}

.messageItem:hover .editButton {
  opacity: 1;
}

.editContainer .editButton {
  color: #22c55e;

  &:hover {
    color: #16a34a;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.itemContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 4px;
}

.moreButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 4px;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  border-radius: 4px;

  &:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.aiNav {
  width: 100%;
  padding: 8px;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--ai-color-text-secondary);
  margin-bottom: 2px;

  &:hover {
    background: var(--ai-color-bg-hover);
    color: var(--ai-color-text-primary);
  }

  &:active {
    transform: scale(0.98);
  }
}

.navIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.navTitle {
  font-size: 14px;
  font-weight: 500;
}

.sidebarPlaceholder {
  flex-shrink: 0;
  transition: width var(--transition-smooth);

  &.expanded {
    width: 260px;
  }

  &.collapsed {
    width: 56px;
  }
}

.bottomBar {
  padding: 12px;
  display: flex;
  justify-content: center;
  margin-bottom: 12px;

  &.collapsed {
    padding: 0px 0;
  }
}
