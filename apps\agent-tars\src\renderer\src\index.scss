@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Colors */
  --ai-color-primary: #3b82f6; /* blue-500 */
  --ai-color-primary-hover: #60a5fa; /* blue-400 */
  --ai-color-primary-active: #2563eb; /* blue-600 */
  --ai-color-primary-bg: #dbeafe; /* blue-100 */

  /* Text Colors */
  --ai-color-text-primary: #1a1a1a;
  --ai-color-text-secondary: #454545;
  --ai-color-text-tertiary: #666666;
  --ai-color-text-quaternary: #999999;

  /* Background Colors */
  --ai-color-bg-primary: #ffffff;
  --ai-color-bg-secondary: #fafafa;
  --ai-color-bg-tertiary: #f5f6f7;
  --ai-color-bg-quaternary: #f0f0f0;
  --ai-color-bg-hover: #f0f0f0;
  --ai-color-bg-active: #ebedf0;

  /* Border Colors */
  --ai-color-border: #f0f0f0;
  --ai-color-border-hover: #e0e0e0;

  /* Shadow */
  --ai-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ai-shadow-md: 0 0 20px rgba(0, 0, 0, 0.05);

  /* Border Radius */
  --ai-radius-sm: 4px;
  --ai-radius-md: 8px;
  --radius-lg: 20px;

  /* Transitions */
  --transition-normal: 0.2s ease;
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  --ai-color-scrollbar: rgba(0, 0, 0, 0.2);
  --ai-color-scrollbar-hover: rgba(0, 0, 0, 0.3);
  --ai-color-scrollbar-bg: rgba(0, 0, 0, 0.05);

  /* 滚动条相关变量 */
  --ai-scrollbar-width: 12px;
  --ai-scrollbar-height: 8px;
  --ai-scrollbar-thumb-color: rgba(60, 60, 60, 0.18);
  --ai-scrollbar-track-color: transparent;
  --ai-scrollbar-border-radius: 6px;

  /* Danger Colors */
  --ai-color-danger: #dc2626;
  --ai-color-danger-hover: #ef4444;
  --ai-color-danger-bg: #fee2e2;

  /* Button Colors */
  --ai-color-button-bg: var(--ai-color-bg-tertiary);
  --ai-color-button-hover: var(--ai-color-bg-quaternary);
  --ai-color-button-active: var(--ai-color-bg-active);
  --ai-color-button-text: var(--ai-color-text-tertiary);
  --ai-color-button-text-hover: var(--ai-color-text-primary);
}

.dark {
  /* Colors */
  --ai-color-primary: #3b82f6; /* blue-500 */
  --ai-color-primary-hover: #60a5fa; /* blue-400 */
  --ai-color-primary-active: #2563eb; /* blue-600 */
  --ai-color-primary-bg: #1e3a8a; /* blue-900，暗色主题也相应调整深度 */

  /* Text Colors */
  --ai-color-text-primary: #e6e6e6;
  --ai-color-text-secondary: #cccccc;
  --ai-color-text-tertiary: #999999;
  --ai-color-text-quaternary: #666666;

  /* Background Colors */
  --ai-color-bg-primary: #1a1a1a;
  --ai-color-bg-secondary: #242424;
  --ai-color-bg-tertiary: #2a2a2a;
  --ai-color-bg-quaternary: #333333;
  --ai-color-bg-hover: #2c2c2c;
  --ai-color-bg-active: #363636;

  /* Border Colors */
  --ai-color-border: #303030;
  --ai-color-border-hover: #404040;

  /* Shadow */
  --ai-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --ai-shadow-md: 0 0 20px rgba(0, 0, 0, 0.2);

  --ai-color-scrollbar: rgba(255, 255, 255, 0.2);
  --ai-color-scrollbar-hover: rgba(255, 255, 255, 0.3);
  --ai-color-scrollbar-bg: rgba(255, 255, 255, 0.05);

  /* 滚动条相关变量 */
  --ai-scrollbar-width: 12px;
  --ai-scrollbar-height: 8px;
  --ai-scrollbar-thumb-color: rgba(255, 255, 255, 0.18);
  --ai-scrollbar-track-color: transparent;

  /* Danger Colors */
  --ai-color-danger: #ef4444;
  --ai-color-danger-hover: #dc2626;
  --ai-color-danger-bg: #451a1a;

  /* Button Colors */
  --ai-color-button-bg: var(--ai-color-bg-tertiary);
  --ai-color-button-hover: var(--ai-color-bg-quaternary);
  --ai-color-button-active: var(--ai-color-bg-active);
  --ai-color-button-text: var(--ai-color-text-tertiary);
  --ai-color-button-text-hover: var(--ai-color-text-primary);
}

body {
  color: var(--ai-color-text-primary);
  background: var(--ai-color-bg-primary);
}

.scrollbar {
  scrollbar-gutter: stable;

  /* 滚动条大小 */
  &::-webkit-scrollbar {
    width: var(--ai-scrollbar-width);
    height: var(--ai-scrollbar-height);
    transition: all 0.3s;
  }

  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background: var(--ai-scrollbar-track-color);
  }

  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    border-radius: var(--ai-scrollbar-border-radius);
  }

  /* 鼠标悬停时的滑块样式 */
  &:hover::-webkit-scrollbar-thumb {
    border: 3px solid transparent;
    background-color: var(--ai-scrollbar-thumb-color);
    background-clip: content-box;
  }

  /* 横向滚动条滑块 */
  &::-webkit-scrollbar-thumb:horizontal {
    border: 3px solid transparent;
    background-color: var(--ai-scrollbar-thumb-color);
    background-clip: content-box;
  }

  /* 滚动条交叉处 */
  &::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--ai-scrollbar-thumb-color)
    var(--ai-scrollbar-track-color);
}

@keyframes flash {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    transform: translateX(100%);
    opacity: 0.5;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes spin {
  from {
    transform: perspective(100px) rotateX(10deg) rotateY(10deg) rotate(0deg);
  }
  to {
    transform: perspective(100px) rotateX(10deg) rotateY(10deg) rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}
