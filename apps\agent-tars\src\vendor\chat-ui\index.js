var t=Object.defineProperty,e=Object.defineProperties,n=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,i=(e,n,a)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[n]=a,c=(t,e)=>{for(var n in e||(e={}))r.call(e,n)&&i(t,n,e[n]);if(a)for(var n of a(e))o.call(e,n)&&i(t,n,e[n]);return t},s=(t,a)=>e(t,n(a)),d=(t,e)=>{var n={};for(var i in t)r.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&a)for(var i of a(t))e.indexOf(i)<0&&o.call(t,i)&&(n[i]=t[i]);return n},l=(t,e,n)=>(i(t,"symbol"!==typeof e?e+"":e,n),n),u=(t,e,n)=>new Promise(((a,r)=>{var o=t=>{try{c(n.next(t))}catch(e){r(e)}},i=t=>{try{c(n.throw(t))}catch(e){r(e)}},c=t=>t.done?a(t.value):Promise.resolve(t.value).then(o,i);c((n=n.apply(t,e)).next())}));import p from"localforage";var w=class{constructor(t){l(this,"store"),l(this,"cache"),l(this,"id",0),this.store=p.createInstance({name:t}),this.cache=new Map}getMessageIds(t){return u(this,null,(function*(){const e=`messageIds-${t}`;return(yield this.store.getItem(e))||[]}))}getStore(){return this.store}setMessageIds(t,e){return u(this,null,(function*(){const n=`messageIds-${t}`;yield this.store.setItem(n,e)}))}getMessages(t){return u(this,null,(function*(){if(this.cache.has(t))return this.cache.get(t);const e=yield this.getMessageIds(t),n=(yield Promise.all(e.map((t=>this.store.getItem(t))))).filter(Boolean);return this.cache.set(t,n),n}))}uploadMessage(t){return u(this,null,(function*(){if(!t.conversationId)throw new Error("Message must have a conversationId to be uploaded");const e=`${t.conversationId}-${(new Date).getTime()}-${this.id++}`,n=s(c({},t),{id:e});yield this.store.setItem(e,n);const a=yield this.getMessageIds(t.conversationId);a.push(e),yield this.setMessageIds(t.conversationId,a);const r=this.cache.get(t.conversationId)||[];return r.push(n),this.cache.set(t.conversationId,r),n}))}updateMessage(t){return u(this,null,(function*(){if(!t.conversationId||!t.id)throw new Error("Message must have an id and conversationId to be updated");const e=yield this.store.getItem(t.id);if(e){const n=c(c({},e),t);return yield this.store.setItem(t.id,n),n}}))}clearMessages(t){return u(this,null,(function*(){const e=yield this.getMessageIds(t);for(const t of e)yield this.store.removeItem(t);yield this.setMessageIds(t,[]),this.cache.delete(t)}))}deleteMessage(t){return u(this,null,(function*(){yield this.store.removeItem(t);for(const[e,n]of Array.from(this.cache.entries())){const a=n.filter((e=>e.id!==t));if(a.length!==n.length){this.cache.set(e,a);const n=(yield this.getMessageIds(e)).filter((e=>e!==t));yield this.setMessageIds(e,n);break}}}))}};import{atom as g}from"jotai";function v(){return{messagesAtom:g([]),messageLoadingAtom:g(!1),imageLoadingAtom:g(!1),inputTextAtom:g(""),inputFilesAtom:g([]),uploadMessagePromiseRefAtom:g({current:null}),messageEndRefAtom:g({current:null}),messageSendingAtom:g(!1),isUserScrollingRefAtom:g({current:!1})}}var x=v();import{useAtom as b}from"jotai";import{useCallback as y}from"react";var h=(t=>(t.User="user",t.Assistant="assistant",t))(h||{}),f=(t=>(t.PlainText="plain-text",t.File="file",t))(f||{}),m=(t=>(t.Image="image",t.PDF="pdf",t.Text="text",t.Json="json",t.Zip="zip",t.Audio="audio",t.Video="video",t.Keynote="keynote",t.Powerpoint="powerpoint",t.Excel="excel",t.Word="word",t.PPTX="pptx",t.XLSX="xlsx",t.DOCX="docx",t.Other="other",t.JS="js",t.TS="ts",t.JSX="jsx",t.TSX="tsx",t.HTML="html",t.CSS="css",t.SCSS="scss",t.LESS="less",t.YAML="yaml",t.XML="xml",t.TOML="toml",t.Python="py",t.Java="java",t.Rust="rs",t.Swift="swift",t.Go="go",t.C="c",t.CPP="cpp",t.Stylus="stylus",t.PHP="php",t.Ruby="rb",t.Kotlin="kt",t.CSharp="cs",t.Draft="draft",t))(m||{}),k={"image":"#FFB6C1","pdf":"#FF6347","text":"#4682B4","json":"#32CD32","zip":"#FFD700","audio":"#8A2BE2","video":"#FF4500","keynote":"#00CED1","powerpoint":"#FF8C00","excel":"#228B22","word":"#1E90FF","pptx":"#FF7F50","xlsx":"#3CB371","docx":"#4169E1","js":"#FF6347","ts":"#FF6347","jsx":"#FF6347","tsx":"#FF6347","html":"#FF6347","css":"#FF6347","scss":"#FF6347","less":"#FF6347","yaml":"#FF6347","xml":"#FF6347","toml":"#FF6347","py":"#FF6347","rs":"#FF6347","go":"#FF6347","java":"#FF6347","swift":"#FF6347","c":"#FF6347","cpp":"#FF6347","other":"#D3D3D3","stylus":"#FF6347","php":"#FF6347","rb":"#FF6347","kt":"#FF6347","cs":"#FF6347","draft":"#FF6347"};function z({states:t=x,storageDbName:e,customMessageStorage:n=(e?new w(e):void 0),conversationId:a}={states:x,customMessageStorage:void 0,conversationId:void 0}){const{messagesAtom:r,messageLoadingAtom:o,imageLoadingAtom:i,inputTextAtom:l,inputFilesAtom:p,uploadMessagePromiseRefAtom:g,messageEndRefAtom:v,messageSendingAtom:h,isUserScrollingRefAtom:f}=t,[m,k]=b(o),[z,_]=b(i),[X,Y]=b(l),[S,j]=b(p),[C,F]=b(r),[M]=b(g),[I]=b(v),[T,A]=b(h),[N]=b(f),L=y(((t,e)=>{const r=(new Date).getTime(),o=s(c({},t),{timestamp:r,id:`${r}-${Math.random().toString(36).substring(2,15)}`});if(F((t=>[...t,o])),setTimeout((()=>{I.current&&I.current.scrollIntoView({behavior:"smooth",block:"nearest",inline:"nearest"})}),100),(null==e?void 0:e.shouldSyncStorage)&&n&&a){const t=()=>u(this,null,(function*(){const t=o,{id:e}=t,r=d(t,["id"]),i=yield n.uploadMessage(s(c({},r),{conversationId:a}));return i?(F((t=>t.map((t=>t.id===o.id?s(c({},t),{id:i.id}):t)))),M.current=null,i):null}));return M.current?M.current.then((()=>(M.current=t(),M.current))):(M.current=t(),M.current)}return Promise.resolve(o)}),[n,k,F,M,a,I.current]),R=y(((t,e)=>u(this,null,(function*(){var r;const o=null!=(r=null==e?void 0:e.shouldSyncStorage)?r:t.isFinal;let i;const d=new Promise((t=>i=t));F((r=>{let d=r.length-1;if((null==e?void 0:e.messageId)&&(d=r.findIndex((t=>t.id===e.messageId)),-1===d))return r;const l=c(c({},r[d]),t);if(o&&n){const t=()=>l.id?n.updateMessage(s(c({},l),{conversationId:a})):n.uploadMessage(s(c({},l),{conversationId:a}));if(M.current)M.current.then(t).then(i);else{const e=t();e&&"then"in e&&e.then(i)}}const u=[...r];return u[d]=l,u})),o&&(yield d),I.current&&!1!==(null==e?void 0:e.shouldScrollToBottom)&&!N.current&&I.current.scrollIntoView({behavior:"smooth",block:"nearest",inline:"nearest"})}))),[n,F,M,a]),D=y((()=>u(this,null,(function*(){if(!n||!a)return;k(!0);const t=Date.now(),e=yield n.getMessages(a);console.log(`initMessages: ${Date.now()-t}ms`);const r=e.map((t=>s(c({},t),{isFinal:!0}))).filter((t=>t.content));return F(r),k(!1),r}))),[a,n,k,F]),E=y((()=>u(this,null,(function*(){F((t=>t.map((t=>s(c({},t),{isDeleting:!0}))))),setTimeout((()=>{F([])}),250),n&&a&&(yield n.clearMessages(a)),k(!1)}))),[a,n,k,F]),P=y((t=>u(this,[t],(function*({deleteUIState:t=!0,deleteStorageRecord:e=!0}){if(0!==C.length&&(t&&F((t=>t.slice(0,-1))),e)){const t=C[C.length-1];t.id&&n&&(yield n.deleteMessage(t.id))}}))),[C,n,F]),$=y((t=>u(this,null,(function*(){const e=C.find((e=>e.id===t));e&&(e.isDeleting=!0,F([...C]),setTimeout((()=>{F((e=>e.filter((e=>e.id!==t))))}),250),n&&e.id&&(yield n.deleteMessage(e.id)))}))),[C,n,F]);return{addMessage:L,updateMessage:R,messages:C,setMessages:F,initMessages:D,clearMessages:E,deleteLastMessage:P,deleteMessage:$,messageLoading:m,setMessageLoading:k,imageLoading:z,setImageLoading:_,inputText:X,setInputText:Y,inputFiles:S,setInputFiles:j,messageSending:T,setMessageSending:A,messageEndRef:I}}import{useRef as _,useState as X}from"react";import{Tooltip as Y}from"@nextui-org/react";import{jsx as S}from"react/jsx-runtime";function j(t){return S("svg",s(c({xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",viewBox:"0 0 24 24"},t),{children:S("path",{fill:"currentColor",d:"M19 21H8V7h11m0-2H8a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2m-3-4H4a2 2 0 0 0-2 2v14h2V3h12z"})}))}import{jsx as C}from"react/jsx-runtime";var F=t=>C("svg",s(c({width:"32",height:"32",viewBox:"0 0 30 30"},t),{children:C("path",{fill:"#49cd37",d:"m13 24l-9-9l1.414-1.414L13 21.171L26.586 7.586L28 9L13 24z"})}));var M=function(t,e){void 0===e&&(e={});var n=e.insertAt;if(t&&"undefined"!==typeof document){var a=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===n&&a.firstChild?a.insertBefore(r,a.firstChild):a.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}};M(".ai-app-message-copy-button {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 10;\n  border-radius: var(--chat-ui-copy-button-border-radius);\n  padding: 0.3rem;\n  border: 1px solid var(--chat-ui-copy-button-border-color);\n  background-color: var(--chat-ui-copy-button-background-color);\n  color: var(--chat-ui-copy-button-text-color);\n}\n.ai-app-message-copy-button svg {\n  opacity: inherit;\n  width: 16px;\n  height: 16px;\n  transition: all 0.2s ease;\n  color: var(--chat-ui-copy-button-text-color);\n}\n.ai-app-message-copy-button svg:hover {\n  color: var(--chat-ui-copy-button-text-color);\n}\n.ai-app-message-copy-button .ai-app-message-icon-success {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%) scale(0);\n  opacity: 0;\n  color: #00d600;\n}\n\n.ai-app-message-copied .ai-app-message-icon-copy {\n  transform: scale(0.33);\n  opacity: 0;\n}\n.ai-app-message-copied .ai-app-message-icon-success {\n  transform: translate(-50%, -50%) scale(1);\n  opacity: 1;\n  transition-delay: 0.075s;\n}");import{jsx as I,jsxs as T}from"react/jsx-runtime";var A=new Map,N=({getCopyText:t,tooltipText:e="点击复制",successText:n="复制成功！"})=>{const a=_(null),[r,o]=X(!1);return I(Y,{content:r?n:e,children:T("button",{ref:a,className:"ai-app-message-copy-button",onClick:()=>{navigator.clipboard.writeText(t()).then((()=>{const t=a.current,e="ai-app-message-copied";if(t){t.classList.add(e),clearTimeout(A.get(t)),o(!0);const n=setTimeout((()=>{o(!1),t.classList.remove(e),t.blur(),A.delete(t)}),2e3);A.set(t,n)}}))},children:[I(j,{className:"ai-app-message-icon-copy"}),I(F,{className:"ai-app-message-icon-success"})]})})};import{AiOutlineFileExcel as L,AiOutlineFilePpt as R,AiFillAudio as D,AiFillVideoCamera as E,AiFillFileText as P}from"react-icons/ai";import{VscJson as $,VscFilePdf as O,VscFileZip as B,VscFileMedia as W,VscFileCode as H}from"react-icons/vsc";import{FaJs as U,FaPython as q,FaJava as V,FaSass as J,FaReact as K,FaHtml5 as G,FaCss3Alt as Z,FaPhp as Q}from"react-icons/fa";import{SiTypescript as tt,SiRust as et,SiSwift as nt,SiGo as at,SiCplusplus as rt,SiLess as ot,SiStylus as it,SiRuby as ct,SiKotlin as st,SiCsharp as dt}from"react-icons/si";import{jsx as lt}from"react/jsx-runtime";var ut=t=>{const e=(t=>{let e="";const n=new Uint8Array(t),a=n.byteLength;for(let r=0;r<a;r++)e+=String.fromCharCode(n[r]);return window.btoa(e)})(t);return JSON.stringify({data:e})},pt=t=>{var e;const n=null==(e=t.split(".").pop())?void 0:e.toLowerCase();switch(n){case"pdf":return"pdf";case"txt":return"text";case"json":return"json";case"zip":return"zip";case"mp3":case"wav":return"audio";case"mp4":case"avi":return"video";case"key":return"keynote";case"ppt":return"powerpoint";case"xls":return"excel";case"doc":return"word";case"pptx":return"pptx";case"xlsx":return"xlsx";case"docx":return"docx";case"stylus":return"stylus";case"php":return"php";case"rb":return"rb";case"kt":return"kt";case"cs":return"cs";default:return n}},wt={"pdf":t=>lt(O,{size:t}),"text":t=>lt(P,{size:t}),"zip":t=>lt(B,{size:t}),"audio":t=>lt(D,{size:t}),"video":t=>lt(E,{size:t}),"docx":t=>lt(P,{size:t}),"pptx":t=>lt(R,{size:t}),"powerpoint":t=>lt(R,{size:t}),"excel":t=>lt(L,{size:t}),"xlsx":t=>lt(L,{size:t}),"keynote":t=>lt(P,{size:t}),"json":t=>lt($,{size:t}),"word":t=>lt(P,{size:t}),"image":t=>lt(W,{size:t}),"yaml":t=>lt(H,{size:t}),"xml":t=>lt(H,{size:t}),"toml":t=>lt(H,{size:t}),"js":t=>lt(U,{size:t,color:"#F7DF1E"}),"ts":t=>lt(tt,{size:t,color:"#3178C6"}),"jsx":t=>lt(K,{size:t,color:"#61DAFB"}),"tsx":t=>lt(K,{size:t,color:"#61DAFB"}),"html":t=>lt(G,{size:t,color:"#E34F26"}),"css":t=>lt(Z,{size:t,color:"#1572B6"}),"scss":t=>lt(J,{size:t,color:"#CC6699"}),"less":t=>lt(ot,{size:t,color:"#1D365D"}),"py":t=>lt(q,{size:t,color:"#3776AB"}),"java":t=>lt(V,{size:t,color:"#007396"}),"rs":t=>lt(et,{size:t,color:"#B7410E"}),"swift":t=>lt(nt,{size:t,color:"#FA7343"}),"go":t=>lt(at,{size:t,color:"#00ADD8"}),"c":t=>lt(rt,{size:t,color:"#00599C"}),"cpp":t=>lt(rt,{size:t,color:"#00599C"}),"stylus":t=>lt(it,{size:t,color:"#FF6347"}),"php":t=>lt(Q,{size:t,color:"#777BB4"}),"rb":t=>lt(ct,{size:t,color:"#CC342D"}),"kt":t=>lt(st,{size:t,color:"#0095D5"}),"draft":t=>lt(H,{size:t}),"cs":t=>lt(dt,{size:t,color:"#239120"}),"other":t=>lt(H,{size:t})};function gt(t,e=16){const n=pt(t);return(wt[n]||wt.other)(e)}var vt={};function xt(t){return u(this,null,(function*(){const e=t.filter((t=>"image"===t.type)),n=e.map((({content:t})=>t)).map((t=>({role:"user",content:[{type:"image_url",image_url:{url:t}}]})));n.length>0&&n.push({role:"user",content:`The uploaded image links: [${e.map((t=>t.content)).join(", ")}]`});const a=t.filter((t=>"image"!==t.type)).map((t=>({role:"user",content:`This is a ${t.type} file, the content is: \n${t.content}`})));return[...n,...a]}))}function bt(t){return u(this,null,(function*(){const e=t.filter((t=>"image"===t.type)),n=yield function(t){return u(this,null,(function*(){return yield Promise.all(t.map((t=>u(this,null,(function*(){if(vt[t])return vt[t];const e=yield fetch(t),n=yield e.blob();return new Promise(((e,a)=>{const r=new FileReader;r.onload=()=>{const n=r.result;vt[t]=n,e(n)},r.onerror=a,r.readAsDataURL(n)}))})))))}))}(e.map((({content:t})=>t)));return t.map((t=>{if("image"===t.type){const a=e.findIndex((e=>e.content===t.content));return s(c({},t),{content:n[a]})}return t}))}))}function yt(t){return t.reduce(((t,e)=>(e.label&&t.push(e.label),e.items&&e.items.length>0&&t.push(...yt(e.items)),t)),[])}import ht from"jszip";var ft=t=>u(void 0,null,(function*(){const e=yield(n="https://esm.sh/v44/pdfjs-dist@4.4.168/es2021/pdfjs-dist.js",new Function(`return import('${n}')`)());var n;e.GlobalWorkerOptions.workerSrc="https://unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs";const a=yield e.getDocument({data:t}).promise;let r="";for(let t=1;t<=a.numPages;t++){const e=yield a.getPage(t);(yield e.getTextContent()).items.forEach((t=>{"str"in t&&(r+=`${t.str} `)}))}return r.trim().slice(0,1e4)})),mt=(t,e,n,a=!1)=>{t((t=>t.map(((t,r)=>r===e?s(c({},t),{content:n,loading:a}):t))))},kt=(t,e)=>u(void 0,null,(function*(){let n;e((e=>(n=e.length,[...e,{type:"image",content:"",loading:!0,originalFile:t}])));try{const a=new FileReader,r=yield new Promise(((e,n)=>{a.onload=()=>e(a.result),a.onerror=n,a.readAsDataURL(t)}));if("undefined"===typeof n)return;mt(e,n,r,!1)}catch(a){console.error("文件转换失败",a),e((t=>t.filter(((t,e)=>e!==n))))}})),zt=(t,e)=>u(void 0,null,(function*(){const n=pt(t.name),a=t.size/1024;if(t.type.startsWith("image/"))yield kt(t,e);else{let r;e((e=>(r=e.length,[...e,{type:n,filename:t.name,content:"",size:a,loading:!0,originalFile:t}])));const o=new FileReader;o.onload=()=>u(void 0,null,(function*(){let t;var a;t=o.result instanceof ArrayBuffer?"pdf"===n?yield ft(o.result):"zip"===n?yield(a=o.result,u(void 0,null,(function*(){const t=yield ht.loadAsync(a);let e="";for(const[n,a]of Object.entries(t.files))a.dir||(e+=`\n\n--- ${n} ---\n\n${yield a.async("string")}`);return e.trim()}))):ut(o.result):o.result,mt(e,r,t,!1)})),"pdf"===n||"audio"===n||"video"===n||"zip"===n?o.readAsArrayBuffer(t):o.readAsText(t)}}));import{useState as _t}from"react";M('@charset "UTF-8";\n.filePreviewContainer_28e0b {\n  display: flex;\n  gap: 8px;\n  margin-right: 8px;\n  width: 100%;\n  overflow-x: auto;\n}\n\n.filePreviewWrapper_28e0b {\n  display: flex;\n  position: relative;\n}\n\n.imagePreview_28e0b {\n  display: flex;\n  max-width: 400px;\n  width: 100%;\n  height: 100%;\n  border: 1px solid #ccc;\n  border-radius: 8px;\n  position: relative;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n  object-fit: cover;\n}\n\n.imagePreview_28e0b.visible_28e0b {\n  opacity: 1;\n}\n\n.imagePreview_28e0b:hover .deleteIcon_28e0b {\n  display: flex; /* 显示删除图标 */\n}\n\n.deleteIcon_28e0b {\n  position: absolute;\n  z-index: 10;\n  top: -8px;\n  right: -5px;\n  border-radius: 50%;\n  cursor: pointer;\n  font-size: 18px;\n  line-height: 12px;\n  width: 24px;\n  height: 24px;\n}\n\n.fullscreenOverlay_28e0b {\n  z-index: 1000;\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.fullscreenImage_28e0b {\n  max-width: 90%;\n  max-height: 90%;\n  animation: fadeIn_28e0b 0.3s ease;\n}\n\n@keyframes fadeIn_28e0b {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}');var Xt="filePreviewContainer_28e0b",Yt="filePreviewWrapper_28e0b",St="imagePreview_28e0b",jt="deleteIcon_28e0b",Ct="fullscreenOverlay_28e0b",Ft="fullscreenImage_28e0b";import{Spinner as Mt,Tooltip as It}from"@nextui-org/react";import{jsx as Tt,jsxs as At}from"react/jsx-runtime";var Nt=t=>At("svg",s(c({viewBox:"64 64 896 896",focusable:"false"},t),{children:[Tt("path",{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64z",fill:"#f9f9f9"}),Tt("path",{d:"M639.98 338.88h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.*********** 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .***********.09l45.02 45.02a.2.2 0 ***********.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.***********.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z",fill:"#000000"})]}));import{Fragment as Lt,jsx as Rt,jsxs as Dt}from"react/jsx-runtime";var Et=({file:t})=>{const e=void 0===t.size?"":t.size<1024?`${t.size.toFixed(2)} KB`:`${(t.size/1024).toFixed(2)} MB`;return Dt("div",{className:"flex gap-2 p-2 pr-4",style:{border:"1px solid #ccc",borderRadius:"10px"},children:[Rt("div",{className:"flex justify-center items-center",style:{width:"44px",height:"100%",borderRadius:6,backgroundColor:k[t.type],color:"var(--chat-ui-text-color)",fontSize:24},children:gt(t.filename||"")}),Dt("div",{className:"flex flex-col text-sm gap-1",children:[Rt(It,{content:t.filename,children:Rt("div",{style:{maxWidth:"200px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",fontWeight:600},children:t.filename})}),Dt("div",{className:"flex gap-2 text-sm",children:[Rt("div",{className:"text-gray-400 font-light",children:t.type}),e?Rt("div",{className:"text-gray-400 font-light",children:e}):null]})]})]})};function Pt({file:t,handleDelete:e,showDeleteIcon:n=!0,itemStyle:a}){const[r,o]=_t(!1),[i,c]=_t(!1),s=()=>{"image"===t.type&&(c(!0),document.body.style.overflow="hidden")};return Dt(Lt,{children:[Dt("div",{className:Yt,onMouseEnter:()=>o(!0),onMouseLeave:()=>o(!1),style:a,children:[!0===t.loading?Rt("div",{style:{width:"56px",height:"56px"},className:"flex justify-center items-center",children:Rt(Mt,{size:"sm"})}):"image"===t.type?Rt("img",{src:t.content,className:St,alt:"pasted",style:a,onClick:s}):Rt(Et,{file:t}),n?Rt("button",{onClick:e,className:jt,style:{display:r?"block":"none"},children:Rt("div",{style:{border:"1px solid #ccc",borderRadius:"50%"},children:Rt(Nt,{})})}):null]}),i?Rt("div",{className:Ct,onClick:()=>{c(!1),document.body.style.overflow="auto"},children:Rt("img",{src:t.content,alt:"fullscreen",className:Ft})}):null]})}function $t({files:t,handleDelete:e,showDeleteIcon:n=!0,containerStyle:a={},itemStyle:r={}}){const o={width:"56px",height:"56px"};return Rt("div",{className:`${Xt}`,style:c({marginTop:t.length>0?"-8px":"0",marginBottom:t.length>0?"8px":"0",paddingTop:t.length>0?"8px":"0"},a),children:t.map(((t,a)=>Rt(Pt,{file:t,handleDelete:()=>null==e?void 0:e(a),showDeleteIcon:n,itemStyle:c(c({},"image"===t.type?o:{}),r)},a)))})}function Ot(t,e){for(const n of t){if(n.label===e)return n;if(n.items){const t=Ot(n.items,e);if(t)return t}}}function Bt(t,e){const n=[],a=Object.keys(e).join(""),r=new RegExp(`([${a}][\\w\\u4e00-\\u9fa5:\\/\\.\\-]+)(\\s|$)`,"g"),o=t.split(r);for(let i=0;i<o.length;i++){const t=o[i];if(t&&Object.keys(e).some((e=>t.startsWith(e)))){const a=t[0],r=t.slice(1);if(!r)continue;const o=Ot(e[a].data,r);o?n.push({type:"commandText",content:o.name}):n.push({type:"text",content:t.trim()}),i++}else t.trim()&&n.push({type:"text",content:t.trim()})}return n}import{useState as Wt,useEffect as Ht,useCallback as Ut,useRef as qt,useMemo as Vt}from"react";import{Card as Jt,Spinner as Kt}from"@nextui-org/react";import{motion as Gt,AnimatePresence as Zt}from"framer-motion";import{FiArrowLeft as Qt,FiArrowRight as te}from"react-icons/fi";M(".ai-app-at-panel {\n  position: absolute;\n  left: 0;\n  right: 0;\n  margin: 0.5rem 0;\n  max-width: 100%;\n  z-index: 1000;\n}\n.ai-app-at-panel.above {\n  bottom: 100%;\n  margin-bottom: 0.5rem;\n}\n.ai-app-at-panel.above .ai-app-at-panel-card {\n  margin-bottom: 0.5rem;\n}\n.ai-app-at-panel.below {\n  width: 90%;\n  margin: auto;\n  top: 30%;\n  margin-top: 0.5rem;\n}\n.ai-app-at-panel.below .ai-app-at-panel-card {\n  margin-top: 0.5rem;\n}\n.ai-app-at-panel-no-results {\n  padding: 4px 16px;\n  color: var(--nextui-colors-accents6);\n  font-size: 14px;\n  text-align: center;\n}\n\n.ai-app-at-panel-card {\n  padding: var(--chat-ui-at-panel-padding);\n  background-color: var(--chat-ui-at-panel-bg);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border-radius: var(--chat-ui-at-panel-border-radius);\n  border: 1px solid var(--chat-ui-at-panel-border);\n  max-height: 400px;\n  overflow: auto !important;\n  overflow-x: hidden;\n}\n.ai-app-at-panel-card::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n.ai-app-at-panel-card::-webkit-scrollbar-track {\n  background: var(--chat-ui-at-panel-scrollbar-track);\n}\n.ai-app-at-panel-card::-webkit-scrollbar-thumb {\n  background: var(--chat-ui-at-panel-scrollbar-thumb);\n  border-radius: 3px;\n}\n.ai-app-at-panel-card::-webkit-scrollbar-thumb:hover {\n  background: var(--chat-ui-at-panel-scrollbar-thumb-hover);\n}\n\n.ai-app-at-panel-item {\n  display: flex;\n  align-items: center;\n  gap: var(--chat-ui-at-panel-item-gap);\n  justify-content: space-between;\n  padding: var(--chat-ui-at-panel-item-padding);\n  border-radius: var(--chat-ui-at-panel-item-border-radius);\n  font-size: 0.75rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  color: var(--chat-ui-at-panel-text);\n  width: fit-content;\n  min-width: 100%;\n}\n\n.ai-app-at-panel-item:hover {\n  background-color: var(--chat-ui-at-panel-item-hover);\n}\n\n.ai-app-at-panel-item-selected {\n  background-color: var(--chat-ui-at-panel-item-selected);\n}\n\n.ai-app-at-panel-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.ai-app-at-panel-item-name {\n  margin-left: 0.75rem;\n  font-size: 0.75rem;\n  color: var(--chat-ui-at-panel-text-dark);\n}\n\n.ai-app-at-panel-item-label {\n  flex-shrink: 0;\n}\n\n.ai-app-at-panel-item-description {\n  font-size: 0.675rem;\n  color: var(--chat-ui-at-panel-text-light);\n  margin-left: 0.5rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 400px;\n  direction: rtl;\n  text-align: left;\n}");import{jsx as ee,jsxs as ne}from"react/jsx-runtime";function ae({config:t,isShow:e,setIsShow:n,inputRef:a,filterText:r,setInputText:o,setFilterText:i,command:c,onSelect:s,isLoading:d}){const[l,u]=Wt(0),[p,w]=Wt({items:t.data,label:"",name:"",description:""}),[g,v]=Wt([p]),x=1===g.length,b=qt(null),y=qt(!1),[h,f]=Wt(!0);Ht((()=>{if(e&&a.current&&b.current){const t=a.current.getBoundingClientRect(),e=b.current.offsetHeight,n=window.innerHeight,r=t.top,o=n-t.bottom;f(!(r<e&&o>=e))}}),[e,r,a]);const m=Vt((()=>{var t,e;const n=g[g.length-1];if(!r)return null==(t=n.items)?void 0:t.slice(0,30);const a=null==(e=n.items)?void 0:e.flatMap((t=>t.items||t));return r&&a?a.filter((t=>{var e;return t.label.toLowerCase().includes(r.toLowerCase())||(null==(e=t.description)?void 0:e.toLowerCase().includes(r.toLowerCase()))})).slice(0,30):null==a?void 0:a.slice(0,30)}),[g,r]);Ht((()=>{u(0)}),[r]);const k=Ut((t=>{if(t.items&&t.items.length>0)v((e=>[...e,t])),w(t),u(0);else{const e=a.current;if(e){const n=e.selectionStart,a=e.value.substring(0,n),r=e.value.substring(n),s=a.lastIndexOf(c),d=`${a.substring(0,s)}${c}${t.label} ${r}`;o(d),i(""),setTimeout((()=>{e.setSelectionRange(s+t.label.length+2,s+t.label.length+2),e.focus()}),0)}n(!1),null==s||s(c,t)}}),[a,n,s,c,o,i]),z=Ut((()=>{if(g.length>1){const t=g[g.length-2];v((t=>t.slice(0,-1))),w(t),u(0)}else n(!1)}),[g,n]);return Ht((()=>{const r=r=>{if(y.current&&"Enter"===r.key)y.current=!1;else if(e&&!y.current)if("ArrowUp"===r.key||"ArrowDown"===r.key)u((t=>{const e=(null==m?void 0:m.length)||0,n=x?e:e+1;if(0===n)return t;let a;return a="ArrowUp"===r.key?(t-1+n)%n:(t+1)%n,setTimeout((()=>{var t;const e=null==(t=b.current)?void 0:t.querySelector(".ai-app-at-panel-item-selected");null==e||e.scrollIntoView({behavior:"smooth",block:"nearest"})}),0),a})),r.preventDefault(),r.stopPropagation();else if("Enter"===r.key)m&&m.length>0&&(l!==m.length||x?l<m.length&&k(m[l]):z()),r.preventDefault(),r.stopPropagation();else if("Escape"===r.key)n(!1),w({items:t.data,label:"",name:"",description:""}),v([{items:t.data,label:"",name:"",description:""}]),i(""),u(0);else if("Tab"===r.key){const t=a.current;if(!t)return;if(!(null==m?void 0:m.length))return;const e=m[l];if(!e.selectText||!e.selectText.includes("/"))return void setTimeout((()=>{t.focus()}),0);const n=t.selectionStart,r=t.value.substring(0,n),s=t.value.substring(n),d=r.lastIndexOf(c),u=`${r.substring(0,d)}${c}${e.selectText}${s}`;o(u),i(e.selectText),setTimeout((()=>{t.setSelectionRange(u.length,u.length),t.focus()}),0)}},s=()=>{y.current=!0},d=t=>{y.current=!1,i((e=>e+t.data))},p=a.current;return p&&(p.addEventListener("compositionstart",s),p.addEventListener("compositionend",d)),document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r),p&&(p.removeEventListener("compositionstart",s),p.removeEventListener("compositionend",d))}}),[e,l,m,k,z,a,o,n,x,r,t,i,c]),ee(Zt,{children:e?ee(Gt.div,{initial:{opacity:0,y:h?10:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:h?10:-10},transition:{duration:.2},className:"ai-app-at-panel "+(h?"above":"below"),ref:b,children:ne(Jt,{className:"ai-app-at-panel-card",style:{borderRadius:"var(--chat-ui-at-panel-border-radius)"},children:[d?ee("div",{className:"flex justify-center items-center p-4",children:ee(Kt,{size:"sm"})}):m&&m.length>0?m.map(((t,e)=>{var n;return ne(Gt.div,{className:"ai-app-at-panel-item "+(e===l?"ai-app-at-panel-item-selected":""),onClick:()=>k(t),children:[ne("div",{className:"ai-app-at-panel-item-content flex items-center gap-2",children:[t.icon,ee("span",{className:"ai-app-at-panel-item-label",children:null!=(n=t.selectText)?n:t.label}),t.description?ee("span",{className:"ai-app-at-panel-item-description",children:t.description}):null]}),t.items&&t.items.length>0?ee(te,{size:16}):null]},t.name)})):ee("div",{className:"ai-app-at-panel-no-results",children:"无匹配结果（按 Esc 可取消面板）"}),d||x||!p.label?null:ee(Gt.div,{className:"ai-app-at-panel-item "+(l===(null==m?void 0:m.length)?"ai-app-at-panel-item-selected":""),onClick:z,children:ne("div",{className:"ai-app-at-panel-item-content",children:[ee(Qt,{size:16}),ne("span",{className:"ai-app-at-panel-item-name",children:["返回: ",p.label]})]})})]})}):null})}import{jsx as re,jsxs as oe}from"react/jsx-runtime";function ie({size:t=20,className:e}){return oe("svg",{width:t,height:t,viewBox:"0 0 24 24",className:e,xmlns:"http://www.w3.org/2000/svg",children:[re("path",{d:"M12 0h4c2.208 0 4 1.792 4 4s-1.792 4-4 4h-4V0z",fill:"#FF4D3D"}),re("path",{d:"M4 4c0-2.208 1.792-4 4-4h4v8H8C5.792 8 4 6.208 4 4z",fill:"#9747FF"}),re("path",{d:"M4 12c0-2.208 1.792-4 4-4h4v8H8c-2.208 0-4-1.792-4-4z",fill:"#18A0FB"}),re("path",{d:"M8 24c2.208 0 4-1.792 4-4v-4H8c-2.208 0-4 1.792-4 4s1.792 4 4 4z",fill:"#09CF83"}),re("path",{d:"M20 12c0 2.208-1.792 4-4 4s-4-1.792-4-4 1.792-4 4-4 4 1.792 4 4z",fill:"#FF4D3D"})]})}import{Tooltip as ce}from"@nextui-org/react";import{useRef as se,useCallback as de,memo as le,useState as ue,useEffect as pe,forwardRef as we,useImperativeHandle as ge}from"react";import{jsx as ve}from"react/jsx-runtime";function xe(t){return ve("svg",s(c({xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",viewBox:"0 0 24 24"},t),{children:ve("path",{fill:"currentColor",d:"M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6z"})}))}import{jsx as be}from"react/jsx-runtime";function ye(t){return be("svg",s(c({xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",viewBox:"0 0 24 24"},t),{children:be("path",{fill:"currentColor",d:"M16.5 6v11.5a4 4 0 0 1-4 4a4 4 0 0 1-4-4V5A2.5 2.5 0 0 1 11 2.5A2.5 2.5 0 0 1 13.5 5v10.5a1 1 0 0 1-1 1a1 1 0 0 1-1-1V6H10v9.5a2.5 2.5 0 0 0 2.5 2.5a2.5 2.5 0 0 0 2.5-2.5V5a4 4 0 0 0-4-4a4 4 0 0 0-4 4v12.5a5.5 5.5 0 0 0 5.5 5.5a5.5 5.5 0 0 0 5.5-5.5V6z"})}))}M(".ai-app-input-section-container {\n  position: relative;\n  padding: 1px;\n  background: var(--chat-ui-input-textarea-bg);\n  background-size: 300% 300%;\n  animation: shineAnimation 6s linear infinite;\n  border-radius: 8px;\n  margin: 0;\n  width: 100%;\n}\n\n.ai-app-input-section-wrapper {\n  position: relative;\n  border-radius: 7px;\n  padding: 16px;\n  display: flex;\n  flex-direction: column;\n}\n\n.ai-app-input-textarea {\n  width: 100%;\n  min-height: 40px;\n  border: none;\n  font-size: 14px;\n  background: transparent;\n  color: var(--chat-ui-input-textarea-color);\n  outline: none;\n  resize: none;\n  padding: 0;\n  margin: 0;\n  overflow: auto;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n}\n.ai-app-input-textarea::placeholder {\n  font-size: 14px;\n  color: var(--chat-ui-input-textarea-placeholder-color);\n}\n.ai-app-input-textarea:focus {\n  outline: none;\n}\n\n.ai-app-input-section-button-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n}\n\n.ai-app-input-section-button-left {\n  display: flex;\n  gap: 4px;\n}\n\n.ai-app-input-section-menu-button {\n  padding: 8px;\n  background-color: transparent;\n  border: none;\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 16px;\n  color: #555;\n}\n.ai-app-input-section-menu-button:hover {\n  background-color: var(--chat-ui-input-section-menu-button-hover-bg);\n  transform: scale(1.1);\n}\n\n.ai-app-send-button {\n  padding: 8px 8px;\n  background-color: #016fee;\n  color: #ffffff;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  opacity: 0;\n  transform: scale(0.8);\n  animation: fadeInScale 0.3s forwards;\n  border-radius: 12px;\n  font-weight: 600;\n  box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);\n}\n.ai-app-send-button:hover {\n  transform: translateY(-2px) scale(1.05);\n  background-color: #2980b9;\n  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.5);\n}\n.ai-app-send-button.stop {\n  background-color: #ff6347;\n  box-shadow: 0 2px 10px rgba(255, 99, 71, 0.3);\n}\n.ai-app-send-button.stop:hover {\n  transform: translateY(-2px) scale(1.05);\n  background-color: #e05734;\n  box-shadow: 0 4px 15px rgba(255, 99, 71, 0.5);\n}\n\n@keyframes shineAnimation {\n  0% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n  100% {\n    background-position: 0% 50%;\n  }\n}\n@keyframes fadeInScale {\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}");import{useAtom as he}from"jotai";import{IoArrowUp as fe,IoStop as me}from"react-icons/io5";import ke from"classnames";import{jsx as ze,jsxs as _e}from"react/jsx-runtime";var Xe=le(we((({disableInput:t,userMessages:e,sendMessage:n,abortMessage:a,clearMessages:r,slots:o,style:i={},features:c={},chatStates:s,commandPanelConfig:d,inputPlaceholder:l,onCommandTrigger:p,onCommandSelect:w,onCommandDelete:g,onClearConversationHistory:v},x)=>{const{inputTextAtom:b,inputFilesAtom:y,messageEndRefAtom:h,imageLoadingAtom:f,messageLoadingAtom:m,messageSendingAtom:k}=s,[z,_]=he(b),[X,Y]=he(y),[S]=he(h),[j]=he(m),[C]=he(f),[F,M]=ue(""),I=se(null),T=se(null),{clearConversationHistory:A,uploadFiles:N}=c,[L,R]=ue(!1),[D,E]=he(k),[P,$]=ue(e.length),[O,B]=ue(!1);pe((()=>{$(e.length)}),[e.length]);const W=de((t=>{Y((e=>{const n=[...e];return n.splice(t,1),n}))}),[Y]),H=de((t=>u(void 0,null,(function*(){if(document.activeElement!==I.current)return;const{files:e}=t.clipboardData;if(e.length>0){const n=e[0];t.preventDefault(),yield zt(n,Y)}}))),[Y]),U=de((()=>{null==v||v(),null==r||r()}),[r,v]),[q,V]=ue(!1),[J,K]=ue(null),G=de(((t,e)=>u(void 0,null,(function*(){if(!(!t.trim()&&!e.length||j||C||q)){E(!0),_(""),Y([]),setTimeout((()=>{var t;null==(t=S.current)||t.scrollIntoView({behavior:"smooth",block:"nearest",inline:"nearest"})}));try{yield null==n?void 0:n(t,e)}finally{E(!1)}}}))),[j,C,q,E,_,Y,S,n]),Z=de((()=>u(void 0,null,(function*(){D?(null==a||a(),E(!1)):yield G(z,X)}))),[D,a,E,G,z,X]),Q=de((t=>u(void 0,null,(function*(){var n,a,r;if(document.activeElement===I.current&&!L&&229!==t.keyCode)if(q&&"Escape"===t.key)V(!1);else{if("ArrowLeft"===t.key||"ArrowRight"===t.key){const e=(null==(n=I.current)?void 0:n.selectionStart)||0,o=(null==(a=I.current)?void 0:a.value)||"";let i=e;const c=yt(Object.values(d||{}).flatMap((t=>t.data)));if("ArrowLeft"===t.key)for(const n of Object.keys(d||{})){const a=c.find((t=>o.substring(0,e).endsWith(`${n}${t}`)));if(a){const r=n.length+a.length;i=e-r,t.preventDefault();break}if(o.substring(0,e).endsWith(n)){i=e-n.length,t.preventDefault();break}}else for(const n of Object.keys(d||{})){const a=c.find((t=>o.substring(e).startsWith(`${n}${t}`)));if(a){const r=n.length+a.length;i=e+r,t.preventDefault();break}if(o.substring(e).startsWith(n)){i=e+n.length,t.preventDefault();break}}i!==e&&(null==(r=I.current)||r.setSelectionRange(i,i))}if("ArrowUp"!==t.key)if("ArrowDown"!==t.key){if("Enter"===t.key){if(t.shiftKey)return;if(!q&&!t.metaKey&&!t.ctrlKey){if(D)return;return t.preventDefault(),t.stopPropagation(),E(!0),yield G(z,X),void E(!1)}}if("Backspace"===t.key&&d&&I.current){const e=I.current.selectionStart,n=I.current.value.substring(0,e),a=I.current.selectionStart,r=I.current.selectionEnd,o=yt(Object.values(d).flatMap((t=>t.data)));for(const c of Object.keys(d))if(n.endsWith(c)&&a===r){t.preventDefault();const n=z.slice(0,e-c.length)+z.slice(e);return _(n),setTimeout((()=>{var t;null==(t=I.current)||t.setSelectionRange(e-c.length,e-c.length)}),0),V(!1),void K(null)}const i=o.find((t=>Object.keys(d).some((e=>n.endsWith(`${e}${t}`)))));if(i){t.preventDefault();const a=Object.keys(d).find((t=>n.endsWith(`${t}${i}`)));if(a){const t=a.length+i.length,n=z.slice(0,e-t)+z.slice(e);_(n),setTimeout((()=>{var n;null==(n=I.current)||n.setSelectionRange(e-t,e-t)}),0);const r=Ot(d[a].data,i);r&&(null==g||g(a,r))}V(!1),K(null)}}}else{const t=I.current;if(!t)return;if(P===e.length-1)return;if(t.selectionStart===t.value.length){const n=e[P+1].content;$((t=>t+1)),_(n),setTimeout((()=>{t.setSelectionRange(n.length,n.length),t.focus()}),0)}}else{const t=I.current;if(!t)return;if(0===P)return;if(0===t.selectionStart){const n=e[P-1].content;$((t=>t-1)),_(n),setTimeout((()=>{t.setSelectionRange(0,0),t.focus()}),0)}}}}))),[L,q,d,P,e,_,D,E,G,z,X,g]);pe((()=>(window.addEventListener("keydown",Q),()=>{window.removeEventListener("keydown",Q)})),[Q]);const tt=de((t=>u(void 0,null,(function*(){const{value:e}=t.target;if(_(e),L)return;const n=t.target.selectionStart,a=e.substring(0,n||0);for(const t of Object.keys(d||{})){const e=a.lastIndexOf(t);if(-1!==e){const n=a.slice(e+t.length);if(!/\s/.test(n))return p&&(B(!0),yield p(t),B(!1)),V(!0),K(t),void M(n)}}V(!1),K(null),M("")}))),[_,L,d,p]),et=de((()=>{R(!0)}),[]),nt=de((()=>{R(!1)}),[]),at=de((t=>u(void 0,null,(function*(){if((null==d?void 0:d[t])&&(p&&(B(!0),yield p(t),B(!1)),V(!0),K(t),M(""),I.current)){const e=I.current.selectionStart||0,n=z.slice(0,e)+t+z.slice(e);_(n),setTimeout((()=>{if(I.current){const n=e+t.length;I.current.setSelectionRange(n,n),I.current.focus()}}),0)}}))),[d,z,_,p]);return ge(x,(()=>({triggerCommand:at,focus:()=>{var t;null==(t=I.current)||t.focus()},getInputTextArea:()=>I.current}))),ze("div",{className:"ai-app-input-section-container",children:_e("div",{className:"ai-app-input-section-wrapper",children:[_e("div",{className:"flex-1",children:[o.beforeInputArea,ze("textarea",{className:"ai-app-input-textarea scrollbar","aria-label":"Prompt",rows:3,ref:I,style:i,disabled:t,placeholder:t?"暂时无法对话":l,onChange:t=>tt(t),onPaste:t=>H(t),value:z,onCompositionStart:et,onCompositionEnd:nt}),o.afterInputArea,d&&q&&J?ze(ae,{config:d[J],isShow:q,setIsShow:V,inputRef:I,filterText:F,setFilterText:M,setInputText:_,onSelect:w,command:J,isLoading:O}):null]}),ze("div",{className:"w-full",children:ze($t,{files:X,handleDelete:W})}),o.secondaryFeatures?ze("div",{className:"ai-app-input-section-button-container",children:o.secondaryFeatures}):null,_e("div",{className:"ai-app-input-section-button-container",children:[_e("div",{className:"ai-app-input-section-button-left",children:[N?ze(ce,{content:"Upload File",children:_e("button",{className:"ai-app-input-section-menu-button",disabled:t,style:{cursor:t?"not-allowed":"pointer"},onClick:()=>{var t;null==(t=T.current)||t.click(),V(!1)},children:[ze(ye,{className:"text-[var(--chat-ui-text-color)]"}),ze("input",{ref:T,type:"file",accept:"*/*",style:{display:"none"},onChange:t=>{var e;const n=null==(e=t.target.files)?void 0:e[0];n&&zt(n,Y)}})]})}):null,A?ze(ce,{content:"Clear History",children:ze("button",{className:"ai-app-input-section-menu-button",disabled:t,style:{cursor:t?"not-allowed":"pointer"},onClick:U,children:ze(xe,{color:"rgb(250, 132, 132)"})})}):null,o.customFeatures]}),o.customSendButton?o.customSendButton:ze("div",{className:"flex gap-2 items-center",children:ze("button",{className:ke("ai-app-send-button",D?"stop":"send"),style:{cursor:t?"not-allowed":"pointer"},disabled:t||q,onClick:Z,children:D?ze(me,{size:18}):ze(fe,{size:20})})})]})]})})})));import{useMemo as Ye,useCallback as Se,memo as je,useRef as Ce,forwardRef as Fe,useImperativeHandle as Me}from"react";import Ie from"classnames";import{useAtom as Te}from"jotai";M('@import url(./preflight.css);\n.\\!container{\n    width: 100% !important;\n}\n.container{\n    width: 100%;\n}\n@media (min-width: 640px){\n    .\\!container{\n        max-width: 640px !important;\n    }\n    .container{\n        max-width: 640px;\n    }\n}\n@media (min-width: 768px){\n    .\\!container{\n        max-width: 768px !important;\n    }\n    .container{\n        max-width: 768px;\n    }\n}\n@media (min-width: 1024px){\n    .\\!container{\n        max-width: 1024px !important;\n    }\n    .container{\n        max-width: 1024px;\n    }\n}\n@media (min-width: 1280px){\n    .\\!container{\n        max-width: 1280px !important;\n    }\n    .container{\n        max-width: 1280px;\n    }\n}\n@media (min-width: 1536px){\n    .\\!container{\n        max-width: 1536px !important;\n    }\n    .container{\n        max-width: 1536px;\n    }\n}\n.sr-only{\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n}\n.pointer-events-none{\n    pointer-events: none;\n}\n.pointer-events-auto{\n    pointer-events: auto;\n}\n.visible{\n    visibility: visible;\n}\n.invisible{\n    visibility: hidden;\n}\n.static{\n    position: static;\n}\n.fixed{\n    position: fixed;\n}\n.absolute{\n    position: absolute;\n}\n.relative{\n    position: relative;\n}\n.sticky{\n    position: sticky;\n}\n.inset-0{\n    inset: 0px;\n}\n.inset-x-0{\n    left: 0px;\n    right: 0px;\n}\n.bottom-0{\n    bottom: 0px;\n}\n.bottom-\\[10\\%\\]{\n    bottom: 10%;\n}\n.bottom-\\[5\\%\\]{\n    bottom: 5%;\n}\n.end-3{\n    inset-inline-end: 0.75rem;\n}\n.end-auto{\n    inset-inline-end: auto;\n}\n.left-0{\n    left: 0px;\n}\n.left-1\\.5{\n    left: 0.375rem;\n}\n.left-1\\/2{\n    left: 50%;\n}\n.left-\\[10\\%\\]{\n    left: 10%;\n}\n.left-\\[5\\%\\]{\n    left: 5%;\n}\n.right-1{\n    right: 0.25rem;\n}\n.right-1\\.5{\n    right: 0.375rem;\n}\n.right-\\[10\\%\\]{\n    right: 10%;\n}\n.right-\\[5\\%\\]{\n    right: 5%;\n}\n.start-2{\n    inset-inline-start: 0.5rem;\n}\n.start-3{\n    inset-inline-start: 0.75rem;\n}\n.start-auto{\n    inset-inline-start: auto;\n}\n.top-0{\n    top: 0px;\n}\n.top-1{\n    top: 0.25rem;\n}\n.top-1\\/2{\n    top: 50%;\n}\n.top-\\[10\\%\\]{\n    top: 10%;\n}\n.top-\\[5\\%\\]{\n    top: 5%;\n}\n.top-\\[calc\\(100\\%_\\+_2px\\)\\]{\n    top: calc(100% + 2px);\n}\n.top-\\[var\\(--navbar-height\\)\\]{\n    top: var(--navbar-height);\n}\n.-z-30{\n    z-index: -30;\n}\n.z-0{\n    z-index: 0;\n}\n.z-10{\n    z-index: 10;\n}\n.z-20{\n    z-index: 20;\n}\n.z-30{\n    z-index: 30;\n}\n.z-40{\n    z-index: 40;\n}\n.z-50{\n    z-index: 50;\n}\n.order-1{\n    order: 1;\n}\n.order-2{\n    order: 2;\n}\n.order-3{\n    order: 3;\n}\n.-m-2{\n    margin: -0.5rem;\n}\n.-m-2\\.5{\n    margin: -0.625rem;\n}\n.m-0{\n    margin: 0px;\n}\n.-mx-1{\n    margin-left: -0.25rem;\n    margin-right: -0.25rem;\n}\n.mx-0{\n    margin-left: 0px;\n    margin-right: 0px;\n}\n.mx-1{\n    margin-left: 0.25rem;\n    margin-right: 0.25rem;\n}\n.mx-2{\n    margin-left: 0.5rem;\n    margin-right: 0.5rem;\n}\n.mx-\\[calc\\(\\(theme\\(spacing\\.5\\)-theme\\(spacing\\.1\\)\\)\\/2\\)\\]{\n    margin-left: calc((1.25rem - 0.25rem) / 2);\n    margin-right: calc((1.25rem - 0.25rem) / 2);\n}\n.mx-\\[calc\\(\\(theme\\(spacing\\.6\\)-theme\\(spacing\\.3\\)\\)\\/2\\)\\]{\n    margin-left: calc((1.5rem - 0.75rem) / 2);\n    margin-right: calc((1.5rem - 0.75rem) / 2);\n}\n.mx-\\[calc\\(\\(theme\\(spacing\\.7\\)-theme\\(spacing\\.5\\)\\)\\/2\\)\\]{\n    margin-left: calc((1.75rem - 1.25rem) / 2);\n    margin-right: calc((1.75rem - 1.25rem) / 2);\n}\n.my-0{\n    margin-top: 0px;\n    margin-bottom: 0px;\n}\n.my-1{\n    margin-top: 0.25rem;\n    margin-bottom: 0.25rem;\n}\n.my-16{\n    margin-top: 4rem;\n    margin-bottom: 4rem;\n}\n.my-\\[calc\\(\\(theme\\(spacing\\.5\\)-theme\\(spacing\\.1\\)\\)\\/2\\)\\]{\n    margin-top: calc((1.25rem - 0.25rem) / 2);\n    margin-bottom: calc((1.25rem - 0.25rem) / 2);\n}\n.my-\\[calc\\(\\(theme\\(spacing\\.6\\)-theme\\(spacing\\.3\\)\\)\\/2\\)\\]{\n    margin-top: calc((1.5rem - 0.75rem) / 2);\n    margin-bottom: calc((1.5rem - 0.75rem) / 2);\n}\n.my-\\[calc\\(\\(theme\\(spacing\\.7\\)-theme\\(spacing\\.5\\)\\)\\/2\\)\\]{\n    margin-top: calc((1.75rem - 1.25rem) / 2);\n    margin-bottom: calc((1.75rem - 1.25rem) / 2);\n}\n.my-auto{\n    margin-top: auto;\n    margin-bottom: auto;\n}\n.-mr-2{\n    margin-right: -0.5rem;\n}\n.-ms-2{\n    margin-inline-start: -0.5rem;\n}\n.mb-1\\.5{\n    margin-bottom: 0.375rem;\n}\n.mb-2{\n    margin-bottom: 0.5rem;\n}\n.mb-5{\n    margin-bottom: 1.25rem;\n}\n.mb-px{\n    margin-bottom: 1px;\n}\n.ml-1{\n    margin-left: 0.25rem;\n}\n.ml-2{\n    margin-left: 0.5rem;\n}\n.mr-2{\n    margin-right: 0.5rem;\n}\n.ms-2{\n    margin-inline-start: 0.5rem;\n}\n.mt-1{\n    margin-top: 0.25rem;\n}\n.mt-2{\n    margin-top: 0.5rem;\n}\n.box-border{\n    box-sizing: border-box;\n}\n.box-content{\n    box-sizing: content-box;\n}\n.line-clamp-1{\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n}\n.block{\n    display: block;\n}\n.inline-block{\n    display: inline-block;\n}\n.inline{\n    display: inline;\n}\n.flex{\n    display: flex;\n}\n.inline-flex{\n    display: inline-flex;\n}\n.table{\n    display: table;\n}\n.grid{\n    display: grid;\n}\n.inline-grid{\n    display: inline-grid;\n}\n.hidden{\n    display: none;\n}\n.\\!h-auto{\n    height: auto !important;\n}\n.h-1{\n    height: 0.25rem;\n}\n.h-1\\.5{\n    height: 0.375rem;\n}\n.h-10{\n    height: 2.5rem;\n}\n.h-12{\n    height: 3rem;\n}\n.h-14{\n    height: 3.5rem;\n}\n.h-16{\n    height: 4rem;\n}\n.h-2{\n    height: 0.5rem;\n}\n.h-2\\.5{\n    height: 0.625rem;\n}\n.h-3{\n    height: 0.75rem;\n}\n.h-3\\.5{\n    height: 0.875rem;\n}\n.h-4{\n    height: 1rem;\n}\n.h-40{\n    height: 10rem;\n}\n.h-5{\n    height: 1.25rem;\n}\n.h-6{\n    height: 1.5rem;\n}\n.h-7{\n    height: 1.75rem;\n}\n.h-8{\n    height: 2rem;\n}\n.h-9{\n    height: 2.25rem;\n}\n.h-\\[--visual-viewport-height\\]{\n    height: var(--visual-viewport-height);\n}\n.h-\\[100dvh\\]{\n    height: 100dvh;\n}\n.h-\\[2px\\]{\n    height: 2px;\n}\n.h-\\[calc\\(100dvh_-_var\\(--navbar-height\\)\\)\\]{\n    height: calc(100dvh - var(--navbar-height));\n}\n.h-\\[var\\(--navbar-height\\)\\]{\n    height: var(--navbar-height);\n}\n.h-\\[var\\(--picker-height\\)\\]{\n    height: var(--picker-height);\n}\n.h-auto{\n    height: auto;\n}\n.h-divider{\n    height: var(--nextui-divider-weight);\n}\n.h-fit{\n    height: fit-content;\n}\n.h-full{\n    height: 100%;\n}\n.h-px{\n    height: 1px;\n}\n.h-screen{\n    height: 100vh;\n}\n.max-h-64{\n    max-height: 16rem;\n}\n.max-h-\\[calc\\(100\\%_-_8rem\\)\\]{\n    max-height: calc(100% - 8rem);\n}\n.min-h-10{\n    min-height: 2.5rem;\n}\n.min-h-12{\n    min-height: 3rem;\n}\n.min-h-14{\n    min-height: 3.5rem;\n}\n.min-h-16{\n    min-height: 4rem;\n}\n.min-h-3{\n    min-height: 0.75rem;\n}\n.min-h-3\\.5{\n    min-height: 0.875rem;\n}\n.min-h-4{\n    min-height: 1rem;\n}\n.min-h-5{\n    min-height: 1.25rem;\n}\n.min-h-6{\n    min-height: 1.5rem;\n}\n.min-h-7{\n    min-height: 1.75rem;\n}\n.min-h-8{\n    min-height: 2rem;\n}\n.min-h-\\[100dvh\\]{\n    min-height: 100dvh;\n}\n.min-h-\\[32px\\]{\n    min-height: 32px;\n}\n.w-0{\n    width: 0px;\n}\n.w-1{\n    width: 0.25rem;\n}\n.w-1\\.5{\n    width: 0.375rem;\n}\n.w-10{\n    width: 2.5rem;\n}\n.w-12{\n    width: 3rem;\n}\n.w-14{\n    width: 3.5rem;\n}\n.w-2{\n    width: 0.5rem;\n}\n.w-2\\.5{\n    width: 0.625rem;\n}\n.w-3{\n    width: 0.75rem;\n}\n.w-3\\.5{\n    width: 0.875rem;\n}\n.w-4{\n    width: 1rem;\n}\n.w-5{\n    width: 1.25rem;\n}\n.w-6{\n    width: 1.5rem;\n}\n.w-7{\n    width: 1.75rem;\n}\n.w-8{\n    width: 2rem;\n}\n.w-9{\n    width: 2.25rem;\n}\n.w-\\[80\\%\\]{\n    width: 80%;\n}\n.w-\\[calc\\(100\\%_-_16px\\)\\]{\n    width: calc(100% - 16px);\n}\n.w-\\[calc\\(100\\%_-_theme\\(spacing\\.6\\)\\)\\]{\n    width: calc(100% - 1.5rem);\n}\n.w-\\[calc\\(var\\(--visible-months\\)_\\*_var\\(--calendar-width\\)\\)\\]{\n    width: calc(var(--visible-months) * var(--calendar-width));\n}\n.w-auto{\n    width: auto;\n}\n.w-divider{\n    width: var(--nextui-divider-weight);\n}\n.w-fit{\n    width: fit-content;\n}\n.w-full{\n    width: 100%;\n}\n.w-max{\n    width: max-content;\n}\n.w-px{\n    width: 1px;\n}\n.w-screen{\n    width: 100vw;\n}\n.min-w-10{\n    min-width: 2.5rem;\n}\n.min-w-12{\n    min-width: 3rem;\n}\n.min-w-16{\n    min-width: 4rem;\n}\n.min-w-20{\n    min-width: 5rem;\n}\n.min-w-24{\n    min-width: 6rem;\n}\n.min-w-3{\n    min-width: 0.75rem;\n}\n.min-w-3\\.5{\n    min-width: 0.875rem;\n}\n.min-w-4{\n    min-width: 1rem;\n}\n.min-w-5{\n    min-width: 1.25rem;\n}\n.min-w-6{\n    min-width: 1.5rem;\n}\n.min-w-7{\n    min-width: 1.75rem;\n}\n.min-w-8{\n    min-width: 2rem;\n}\n.min-w-9{\n    min-width: 2.25rem;\n}\n.min-w-\\[200px\\]{\n    min-width: 200px;\n}\n.min-w-full{\n    min-width: 100%;\n}\n.min-w-max{\n    min-width: max-content;\n}\n.min-w-min{\n    min-width: min-content;\n}\n.max-w-2xl{\n    max-width: 42rem;\n}\n.max-w-3xl{\n    max-width: 48rem;\n}\n.max-w-4xl{\n    max-width: 56rem;\n}\n.max-w-5xl{\n    max-width: 64rem;\n}\n.max-w-\\[1024px\\]{\n    max-width: 1024px;\n}\n.max-w-\\[1280px\\]{\n    max-width: 1280px;\n}\n.max-w-\\[1536px\\]{\n    max-width: 1536px;\n}\n.max-w-\\[270px\\]{\n    max-width: 270px;\n}\n.max-w-\\[640px\\]{\n    max-width: 640px;\n}\n.max-w-\\[768px\\]{\n    max-width: 768px;\n}\n.max-w-fit{\n    max-width: fit-content;\n}\n.max-w-full{\n    max-width: 100%;\n}\n.max-w-lg{\n    max-width: 32rem;\n}\n.max-w-md{\n    max-width: 28rem;\n}\n.max-w-sm{\n    max-width: 24rem;\n}\n.max-w-xl{\n    max-width: 36rem;\n}\n.max-w-xs{\n    max-width: 20rem;\n}\n.flex-1{\n    flex: 1 1 0%;\n}\n.flex-auto{\n    flex: 1 1 auto;\n}\n.flex-initial{\n    flex: 0 1 auto;\n}\n.flex-none{\n    flex: none;\n}\n.flex-shrink-0{\n    flex-shrink: 0;\n}\n.shrink-0{\n    flex-shrink: 0;\n}\n.flex-grow{\n    flex-grow: 1;\n}\n.basis-0{\n    flex-basis: 0px;\n}\n.table-auto{\n    table-layout: auto;\n}\n.table-fixed{\n    table-layout: fixed;\n}\n.border-collapse{\n    border-collapse: collapse;\n}\n.origin-center{\n    transform-origin: center;\n}\n.origin-left{\n    transform-origin: left;\n}\n.origin-right{\n    transform-origin: right;\n}\n.origin-top{\n    transform-origin: top;\n}\n.origin-top-left{\n    transform-origin: top left;\n}\n.-translate-x-1\\/2{\n    --tw-translate-x: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2{\n    --tw-translate-y: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-1{\n    --tw-translate-x: 0.25rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-1\\/2{\n    --tw-translate-x: 50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-1{\n    --tw-translate-y: 0.25rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-1\\/2{\n    --tw-translate-y: 50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-0{\n    --tw-rotate: 0deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-180{\n    --tw-rotate: 180deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-0{\n    --tw-scale-x: 0;\n    --tw-scale-y: 0;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-100{\n    --tw-scale-x: 1;\n    --tw-scale-y: 1;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-105{\n    --tw-scale-x: 1.05;\n    --tw-scale-y: 1.05;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-50{\n    --tw-scale-x: .5;\n    --tw-scale-y: .5;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform{\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes drip-expand{\n    0%{\n        opacity: 0.2;\n        transform: scale(0);\n    }\n    100%{\n        opacity: 0;\n        transform: scale(2);\n    }\n}\n.animate-drip-expand{\n    animation: drip-expand 420ms linear;\n}\n@keyframes indeterminate-bar{\n    0%{\n        transform: translateX(-50%) scaleX(0.2);\n    }\n    100%{\n        transform: translateX(100%) scaleX(1);\n    }\n}\n.animate-indeterminate-bar{\n    animation: indeterminate-bar 1.5s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite normal none running;\n}\n.animate-none{\n    animation: none;\n}\n@keyframes spinner-spin{\n    0%{\n        transform: rotate(0deg);\n    }\n    100%{\n        transform: rotate(360deg);\n    }\n}\n.animate-spinner-ease-spin{\n    animation: spinner-spin 0.8s ease infinite;\n}\n@keyframes spinner-spin{\n    0%{\n        transform: rotate(0deg);\n    }\n    100%{\n        transform: rotate(360deg);\n    }\n}\n.animate-spinner-linear-spin{\n    animation: spinner-spin 0.8s linear infinite;\n}\n.cursor-default{\n    cursor: default;\n}\n.cursor-grab{\n    cursor: grab;\n}\n.cursor-not-allowed{\n    cursor: not-allowed;\n}\n.cursor-pointer{\n    cursor: pointer;\n}\n.cursor-text{\n    cursor: text;\n}\n.touch-none{\n    touch-action: none;\n}\n.select-none{\n    user-select: none;\n}\n.resize-none{\n    resize: none;\n}\n.snap-y{\n    scroll-snap-type: y var(--tw-scroll-snap-strictness);\n}\n.snap-mandatory{\n    --tw-scroll-snap-strictness: mandatory;\n}\n.snap-center{\n    scroll-snap-align: center;\n}\n.scroll-py-6{\n    scroll-padding-top: 1.5rem;\n    scroll-padding-bottom: 1.5rem;\n}\n.list-none{\n    list-style-type: none;\n}\n.appearance-none{\n    appearance: none;\n}\n.grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n}\n.flex-row{\n    flex-direction: row;\n}\n.flex-row-reverse{\n    flex-direction: row-reverse;\n}\n.flex-col{\n    flex-direction: column;\n}\n.flex-col-reverse{\n    flex-direction: column-reverse;\n}\n.flex-wrap{\n    flex-wrap: wrap;\n}\n.flex-nowrap{\n    flex-wrap: nowrap;\n}\n.place-content-center{\n    place-content: center;\n}\n.items-start{\n    align-items: flex-start;\n}\n.items-end{\n    align-items: flex-end;\n}\n.items-center{\n    align-items: center;\n}\n.justify-start{\n    justify-content: flex-start;\n}\n.justify-end{\n    justify-content: flex-end;\n}\n.justify-center{\n    justify-content: center;\n}\n.justify-between{\n    justify-content: space-between;\n}\n.\\!gap-0{\n    gap: 0px !important;\n}\n.gap-0{\n    gap: 0px;\n}\n.gap-0\\.5{\n    gap: 0.125rem;\n}\n.gap-1{\n    gap: 0.25rem;\n}\n.gap-1\\.5{\n    gap: 0.375rem;\n}\n.gap-2{\n    gap: 0.5rem;\n}\n.gap-3{\n    gap: 0.75rem;\n}\n.gap-4{\n    gap: 1rem;\n}\n.gap-x-0\\.5{\n    column-gap: 0.125rem;\n}\n.gap-x-2{\n    column-gap: 0.5rem;\n}\n.gap-x-6{\n    column-gap: 1.5rem;\n}\n.gap-y-1\\.5{\n    row-gap: 0.375rem;\n}\n.gap-y-2{\n    row-gap: 0.5rem;\n}\n.space-x-0\\.5 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.125rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.self-center{\n    align-self: center;\n}\n.overflow-auto{\n    overflow: auto;\n}\n.overflow-hidden{\n    overflow: hidden;\n}\n.overflow-visible{\n    overflow: visible;\n}\n.overflow-x-auto{\n    overflow-x: auto;\n}\n.overflow-y-auto{\n    overflow-y: auto;\n}\n.overflow-y-hidden{\n    overflow-y: hidden;\n}\n.overflow-x-scroll{\n    overflow-x: scroll;\n}\n.overflow-y-scroll{\n    overflow-y: scroll;\n}\n.truncate{\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n}\n.text-ellipsis{\n    text-overflow: ellipsis;\n}\n.whitespace-normal{\n    white-space: normal;\n}\n.whitespace-nowrap{\n    white-space: nowrap;\n}\n.break-words{\n    overflow-wrap: break-word;\n}\n.\\!rounded-none{\n    border-radius: 0px !important;\n}\n.rounded{\n    border-radius: 0.25rem;\n}\n.rounded-\\[calc\\(theme\\(borderRadius\\.large\\)\\/1\\.5\\)\\]{\n    border-radius: calc(var(--nextui-radius-large) / 1.5);\n}\n.rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.5\\)\\]{\n    border-radius: calc(var(--nextui-radius-medium) * 0.5);\n}\n.rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.6\\)\\]{\n    border-radius: calc(var(--nextui-radius-medium) * 0.6);\n}\n.rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.7\\)\\]{\n    border-radius: calc(var(--nextui-radius-medium) * 0.7);\n}\n.rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\/2\\)\\]{\n    border-radius: calc(var(--nextui-radius-medium) / 2);\n}\n.rounded-\\[calc\\(theme\\(borderRadius\\.small\\)\\/2\\)\\]{\n    border-radius: calc(var(--nextui-radius-small) / 2);\n}\n.rounded-full{\n    border-radius: 9999px;\n}\n.rounded-large{\n    border-radius: var(--nextui-radius-large);\n}\n.rounded-md{\n    border-radius: 0.375rem;\n}\n.rounded-medium{\n    border-radius: var(--nextui-radius-medium);\n}\n.rounded-none{\n    border-radius: 0px;\n}\n.rounded-small{\n    border-radius: var(--nextui-radius-small);\n}\n.\\!rounded-e-none{\n    border-start-end-radius: 0px !important;\n    border-end-end-radius: 0px !important;\n}\n.\\!rounded-s-none{\n    border-start-start-radius: 0px !important;\n    border-end-start-radius: 0px !important;\n}\n.rounded-b-large{\n    border-bottom-right-radius: var(--nextui-radius-large);\n    border-bottom-left-radius: var(--nextui-radius-large);\n}\n.rounded-b-medium{\n    border-bottom-right-radius: var(--nextui-radius-medium);\n    border-bottom-left-radius: var(--nextui-radius-medium);\n}\n.rounded-b-small{\n    border-bottom-right-radius: var(--nextui-radius-small);\n    border-bottom-left-radius: var(--nextui-radius-small);\n}\n.rounded-t-large{\n    border-top-left-radius: var(--nextui-radius-large);\n    border-top-right-radius: var(--nextui-radius-large);\n}\n.rounded-t-medium{\n    border-top-left-radius: var(--nextui-radius-medium);\n    border-top-right-radius: var(--nextui-radius-medium);\n}\n.rounded-t-small{\n    border-top-left-radius: var(--nextui-radius-small);\n    border-top-right-radius: var(--nextui-radius-small);\n}\n.border{\n    border-width: 1px;\n}\n.border-0{\n    border-width: 0px;\n}\n.border-2{\n    border-width: 2px;\n}\n.border-3{\n    border-width: 3px;\n}\n.border-medium{\n    border-width: var(--nextui-border-width-medium);\n}\n.border-small{\n    border-width: var(--nextui-border-width-small);\n}\n.border-x-\\[calc\\(theme\\(spacing\\.5\\)\\/2\\)\\]{\n    border-left-width: calc(1.25rem / 2);\n    border-right-width: calc(1.25rem / 2);\n}\n.border-x-\\[calc\\(theme\\(spacing\\.6\\)\\/2\\)\\]{\n    border-left-width: calc(1.5rem / 2);\n    border-right-width: calc(1.5rem / 2);\n}\n.border-x-\\[calc\\(theme\\(spacing\\.7\\)\\/2\\)\\]{\n    border-left-width: calc(1.75rem / 2);\n    border-right-width: calc(1.75rem / 2);\n}\n.border-y-\\[calc\\(theme\\(spacing\\.5\\)\\/2\\)\\]{\n    border-top-width: calc(1.25rem / 2);\n    border-bottom-width: calc(1.25rem / 2);\n}\n.border-y-\\[calc\\(theme\\(spacing\\.6\\)\\/2\\)\\]{\n    border-top-width: calc(1.5rem / 2);\n    border-bottom-width: calc(1.5rem / 2);\n}\n.border-y-\\[calc\\(theme\\(spacing\\.7\\)\\/2\\)\\]{\n    border-top-width: calc(1.75rem / 2);\n    border-bottom-width: calc(1.75rem / 2);\n}\n.border-b{\n    border-bottom-width: 1px;\n}\n.border-b-medium{\n    border-bottom-width: var(--nextui-border-width-medium);\n}\n.border-solid{\n    border-style: solid;\n}\n.border-dotted{\n    border-style: dotted;\n}\n.border-none{\n    border-style: none;\n}\n.\\!border-danger{\n    --tw-border-opacity: 1 !important;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity))) !important;\n}\n.border-background{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-border-opacity)));\n}\n.border-danger{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.border-default{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));\n}\n.border-default-200{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-border-opacity)));\n}\n.border-default-300{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-border-opacity)));\n}\n.border-divider{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-divider) / var(--nextui-divider-opacity, var(--tw-border-opacity)));\n}\n.border-foreground{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-border-opacity)));\n}\n.border-primary{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.border-secondary{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.border-success{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.border-transparent{\n    border-color: transparent;\n}\n.border-warning{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.border-x-transparent{\n    border-left-color: transparent;\n    border-right-color: transparent;\n}\n.border-y-transparent{\n    border-top-color: transparent;\n    border-bottom-color: transparent;\n}\n.border-b-current{\n    border-bottom-color: currentColor;\n}\n.border-b-danger{\n    --tw-border-opacity: 1;\n    border-bottom-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.border-b-default{\n    --tw-border-opacity: 1;\n    border-bottom-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));\n}\n.border-b-foreground{\n    --tw-border-opacity: 1;\n    border-bottom-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-border-opacity)));\n}\n.border-b-primary{\n    --tw-border-opacity: 1;\n    border-bottom-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.border-b-secondary{\n    --tw-border-opacity: 1;\n    border-bottom-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.border-b-success{\n    --tw-border-opacity: 1;\n    border-bottom-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.border-b-warning{\n    --tw-border-opacity: 1;\n    border-bottom-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.border-b-white{\n    --tw-border-opacity: 1;\n    border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-l-transparent{\n    border-left-color: transparent;\n}\n.border-r-transparent{\n    border-right-color: transparent;\n}\n.border-s-danger{\n    --tw-border-opacity: 1;\n    border-inline-start-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.border-s-foreground{\n    --tw-border-opacity: 1;\n    border-inline-start-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-border-opacity)));\n}\n.border-s-primary{\n    --tw-border-opacity: 1;\n    border-inline-start-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.border-s-secondary{\n    --tw-border-opacity: 1;\n    border-inline-start-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.border-s-success{\n    --tw-border-opacity: 1;\n    border-inline-start-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.border-s-warning{\n    --tw-border-opacity: 1;\n    border-inline-start-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.border-t-transparent{\n    border-top-color: transparent;\n}\n.\\!bg-danger-50{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity))) !important;\n}\n.bg-background{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-bg-opacity)));\n}\n.bg-background\\/10{\n    background-color: hsl(var(--nextui-background) / 0.1);\n}\n.bg-background\\/70{\n    background-color: hsl(var(--nextui-background) / 0.7);\n}\n.bg-background\\/80{\n    background-color: hsl(var(--nextui-background) / 0.8);\n}\n.bg-content1{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content1) / var(--nextui-content1-opacity, var(--tw-bg-opacity)));\n}\n.bg-content3{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content3) / var(--nextui-content3-opacity, var(--tw-bg-opacity)));\n}\n.bg-current{\n    background-color: currentColor;\n}\n.bg-danger{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.bg-danger-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));\n}\n.bg-danger-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.bg-danger\\/20{\n    background-color: hsl(var(--nextui-danger) / 0.2);\n}\n.bg-default{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));\n}\n.bg-default-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.bg-default-200{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));\n}\n.bg-default-300\\/50{\n    background-color: hsl(var(--nextui-default-300) / 0.5);\n}\n.bg-default-400{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-bg-opacity)));\n}\n.bg-default-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-50) / var(--nextui-default-50-opacity, var(--tw-bg-opacity)));\n}\n.bg-default-500{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-bg-opacity)));\n}\n.bg-default\\/40{\n    background-color: hsl(var(--nextui-default) / 0.4);\n}\n.bg-divider{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-divider) / var(--nextui-divider-opacity, var(--tw-bg-opacity)));\n}\n.bg-foreground{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.bg-foreground\\/10{\n    background-color: hsl(var(--nextui-foreground) / 0.1);\n}\n.bg-overlay\\/30{\n    background-color: hsl(var(--nextui-overlay) / 0.3);\n}\n.bg-overlay\\/50{\n    background-color: hsl(var(--nextui-overlay) / 0.5);\n}\n.bg-primary{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.bg-primary-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-100) / var(--nextui-primary-100-opacity, var(--tw-bg-opacity)));\n}\n.bg-primary-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));\n}\n.bg-primary\\/20{\n    background-color: hsl(var(--nextui-primary) / 0.2);\n}\n.bg-secondary{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.bg-secondary-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-100) / var(--nextui-secondary-100-opacity, var(--tw-bg-opacity)));\n}\n.bg-secondary-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));\n}\n.bg-secondary\\/20{\n    background-color: hsl(var(--nextui-secondary) / 0.2);\n}\n.bg-success{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.bg-success-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));\n}\n.bg-success-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));\n}\n.bg-success\\/20{\n    background-color: hsl(var(--nextui-success) / 0.2);\n}\n.bg-transparent{\n    background-color: transparent;\n}\n.bg-warning{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.bg-warning-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));\n}\n.bg-warning-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));\n}\n.bg-warning\\/20{\n    background-color: hsl(var(--nextui-warning) / 0.2);\n}\n.bg-white{\n    --tw-bg-opacity: 1;\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-stripe-gradient{\n    background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%, transparent 50%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.1) 75%, transparent 75%, transparent);\n}\n.bg-\\[length\\:1\\.25rem_1\\.25rem\\]{\n    background-size: 1.25rem 1.25rem;\n}\n.bg-clip-text{\n    background-clip: text;\n}\n.stroke-current{\n    stroke: currentColor;\n}\n.stroke-default-300\\/50{\n    stroke: hsl(var(--nextui-default-300) / 0.5);\n}\n.object-cover{\n    object-fit: cover;\n}\n.p-0{\n    padding: 0px;\n}\n.p-1{\n    padding: 0.25rem;\n}\n.p-2{\n    padding: 0.5rem;\n}\n.p-2\\.5{\n    padding: 0.625rem;\n}\n.p-3{\n    padding: 0.75rem;\n}\n.p-4{\n    padding: 1rem;\n}\n.\\!px-1{\n    padding-left: 0.25rem !important;\n    padding-right: 0.25rem !important;\n}\n.px-0{\n    padding-left: 0px;\n    padding-right: 0px;\n}\n.px-0\\.5{\n    padding-left: 0.125rem;\n    padding-right: 0.125rem;\n}\n.px-1{\n    padding-left: 0.25rem;\n    padding-right: 0.25rem;\n}\n.px-1\\.5{\n    padding-left: 0.375rem;\n    padding-right: 0.375rem;\n}\n.px-2{\n    padding-left: 0.5rem;\n    padding-right: 0.5rem;\n}\n.px-2\\.5{\n    padding-left: 0.625rem;\n    padding-right: 0.625rem;\n}\n.px-3{\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n}\n.px-4{\n    padding-left: 1rem;\n    padding-right: 1rem;\n}\n.px-5{\n    padding-left: 1.25rem;\n    padding-right: 1.25rem;\n}\n.px-6{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n}\n.py-0{\n    padding-top: 0px;\n    padding-bottom: 0px;\n}\n.py-0\\.5{\n    padding-top: 0.125rem;\n    padding-bottom: 0.125rem;\n}\n.py-1{\n    padding-top: 0.25rem;\n    padding-bottom: 0.25rem;\n}\n.py-1\\.5{\n    padding-top: 0.375rem;\n    padding-bottom: 0.375rem;\n}\n.py-2{\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n}\n.py-2\\.5{\n    padding-top: 0.625rem;\n    padding-bottom: 0.625rem;\n}\n.py-3{\n    padding-top: 0.75rem;\n    padding-bottom: 0.75rem;\n}\n.py-4{\n    padding-top: 1rem;\n    padding-bottom: 1rem;\n}\n.\\!pb-0{\n    padding-bottom: 0px !important;\n}\n.pb-0{\n    padding-bottom: 0px;\n}\n.pb-0\\.5{\n    padding-bottom: 0.125rem;\n}\n.pb-1{\n    padding-bottom: 0.25rem;\n}\n.pb-1\\.5{\n    padding-bottom: 0.375rem;\n}\n.pb-2{\n    padding-bottom: 0.5rem;\n}\n.pb-4{\n    padding-bottom: 1rem;\n}\n.pe-2{\n    padding-inline-end: 0.5rem;\n}\n.pl-0\\.5{\n    padding-left: 0.125rem;\n}\n.pl-1{\n    padding-left: 0.25rem;\n}\n.pr-0\\.5{\n    padding-right: 0.125rem;\n}\n.pr-1{\n    padding-right: 0.25rem;\n}\n.pr-4{\n    padding-right: 1rem;\n}\n.pr-6{\n    padding-right: 1.5rem;\n}\n.ps-2{\n    padding-inline-start: 0.5rem;\n}\n.pt-0{\n    padding-top: 0px;\n}\n.pt-2{\n    padding-top: 0.5rem;\n}\n.text-left{\n    text-align: left;\n}\n.text-center{\n    text-align: center;\n}\n.text-start{\n    text-align: start;\n}\n.text-end{\n    text-align: end;\n}\n.align-middle{\n    vertical-align: middle;\n}\n.font-mono{\n    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;\n}\n.font-sans{\n    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";\n}\n.text-\\[0\\.55rem\\]{\n    font-size: 0.55rem;\n}\n.text-\\[0\\.5rem\\]{\n    font-size: 0.5rem;\n}\n.text-\\[0\\.6rem\\]{\n    font-size: 0.6rem;\n}\n.text-large{\n    font-size: var(--nextui-font-size-large);\n    line-height: var(--nextui-line-height-large);\n}\n.text-lg{\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n}\n.text-medium{\n    font-size: var(--nextui-font-size-medium);\n    line-height: var(--nextui-line-height-medium);\n}\n.text-sm{\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n}\n.text-small{\n    font-size: var(--nextui-font-size-small);\n    line-height: var(--nextui-line-height-small);\n}\n.text-tiny{\n    font-size: var(--nextui-font-size-tiny);\n    line-height: var(--nextui-line-height-tiny);\n}\n.text-xl{\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n}\n.font-light{\n    font-weight: 300;\n}\n.font-medium{\n    font-weight: 500;\n}\n.font-normal{\n    font-weight: 400;\n}\n.font-semibold{\n    font-weight: 600;\n}\n.tabular-nums{\n    --tw-numeric-spacing: tabular-nums;\n    font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\n}\n.leading-\\[32px\\]{\n    line-height: 32px;\n}\n.\\!text-danger{\n    --tw-text-opacity: 1 !important;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity))) !important;\n}\n.\\!text-danger-foreground{\n    --tw-text-opacity: 1 !important;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity))) !important;\n}\n.text-\\[var\\(--chat-ui-text-color\\)\\]{\n    color: var(--chat-ui-text-color);\n}\n.text-background{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-text-opacity)));\n}\n.text-black{\n    --tw-text-opacity: 1;\n    color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\n.text-current{\n    color: currentColor;\n}\n.text-danger{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.text-danger-300{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-300) / var(--nextui-danger-300-opacity, var(--tw-text-opacity)));\n}\n.text-danger-800{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-800) / var(--nextui-danger-800-opacity, var(--tw-text-opacity)));\n}\n.text-danger-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.text-danger\\/80{\n    color: hsl(var(--nextui-danger) / 0.8);\n}\n.text-default-400{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-text-opacity)));\n}\n.text-default-500{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-text-opacity)));\n}\n.text-default-600{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-600) / var(--nextui-default-600-opacity, var(--tw-text-opacity)));\n}\n.text-default-700{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-700) / var(--nextui-default-700-opacity, var(--tw-text-opacity)));\n}\n.text-default-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.text-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-text-opacity)));\n}\n.text-foreground-400{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-400) / var(--nextui-foreground-400-opacity, var(--tw-text-opacity)));\n}\n.text-foreground-500{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-500) / var(--nextui-foreground-500-opacity, var(--tw-text-opacity)));\n}\n.text-foreground-600{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-600) / var(--nextui-foreground-600-opacity, var(--tw-text-opacity)));\n}\n.text-foreground\\/50{\n    color: hsl(var(--nextui-foreground) / 0.5);\n}\n.text-gray-400{\n    --tw-text-opacity: 1;\n    color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.text-inherit{\n    color: inherit;\n}\n.text-primary{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.text-primary-300{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-300) / var(--nextui-primary-300-opacity, var(--tw-text-opacity)));\n}\n.text-primary-700{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-700) / var(--nextui-primary-700-opacity, var(--tw-text-opacity)));\n}\n.text-primary-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.text-primary\\/80{\n    color: hsl(var(--nextui-primary) / 0.8);\n}\n.text-secondary{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.text-secondary-300{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-300) / var(--nextui-secondary-300-opacity, var(--tw-text-opacity)));\n}\n.text-secondary-700{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-700) / var(--nextui-secondary-700-opacity, var(--tw-text-opacity)));\n}\n.text-secondary-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.text-secondary\\/80{\n    color: hsl(var(--nextui-secondary) / 0.8);\n}\n.text-success{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.text-success-400{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-400) / var(--nextui-success-400-opacity, var(--tw-text-opacity)));\n}\n.text-success-600{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));\n}\n.text-success-800{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-800) / var(--nextui-success-800-opacity, var(--tw-text-opacity)));\n}\n.text-success-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.text-success\\/80{\n    color: hsl(var(--nextui-success) / 0.8);\n}\n.text-warning{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.text-warning-400{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-400) / var(--nextui-warning-400-opacity, var(--tw-text-opacity)));\n}\n.text-warning-600{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));\n}\n.text-warning-800{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-800) / var(--nextui-warning-800-opacity, var(--tw-text-opacity)));\n}\n.text-warning-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.text-warning\\/80{\n    color: hsl(var(--nextui-warning) / 0.8);\n}\n.text-white{\n    --tw-text-opacity: 1;\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.underline{\n    text-decoration-line: underline;\n}\n.no-underline{\n    text-decoration-line: none;\n}\n.underline-offset-4{\n    text-underline-offset: 4px;\n}\n.subpixel-antialiased{\n    -webkit-font-smoothing: auto;\n    -moz-osx-font-smoothing: auto;\n}\n.opacity-0{\n    opacity: 0;\n}\n.opacity-100{\n    opacity: 1;\n}\n.opacity-30{\n    opacity: 0.3;\n}\n.opacity-50{\n    opacity: 0.5;\n}\n.opacity-70{\n    opacity: 0.7;\n}\n.opacity-75{\n    opacity: 0.75;\n}\n.opacity-disabled{\n    opacity: var(--nextui-disabled-opacity);\n}\n.shadow{\n    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-\\[0_1px_0px_0_rgba\\(0\\2c 0\\2c 0\\2c 0\\.05\\)\\]{\n    --tw-shadow: 0 1px 0px 0 rgba(0,0,0,0.05);\n    --tw-shadow-colored: 0 1px 0px 0 var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-\\[0px_20px_20px_0px_rgb\\(0_0_0\\/0\\.05\\)\\]{\n    --tw-shadow: 0px 20px 20px 0px rgb(0 0 0/0.05);\n    --tw-shadow-colored: 0px 20px 20px 0px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-large{\n    --tw-shadow: var(--nextui-box-shadow-large);\n    --tw-shadow-colored: var(--nextui-box-shadow-large);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg{\n    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md{\n    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-medium{\n    --tw-shadow: var(--nextui-box-shadow-medium);\n    --tw-shadow-colored: var(--nextui-box-shadow-medium);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-none{\n    --tw-shadow: 0 0 #0000;\n    --tw-shadow-colored: 0 0 #0000;\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm{\n    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-small{\n    --tw-shadow: var(--nextui-box-shadow-small);\n    --tw-shadow-colored: var(--nextui-box-shadow-small);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-black\\/5{\n    --tw-shadow-color: rgb(0 0 0 / 0.05);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-danger\\/40{\n    --tw-shadow-color: hsl(var(--nextui-danger) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-default\\/50{\n    --tw-shadow-color: hsl(var(--nextui-default) / 0.5);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-foreground\\/40{\n    --tw-shadow-color: hsl(var(--nextui-foreground) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-primary\\/40{\n    --tw-shadow-color: hsl(var(--nextui-primary) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-secondary\\/40{\n    --tw-shadow-color: hsl(var(--nextui-secondary) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-success\\/40{\n    --tw-shadow-color: hsl(var(--nextui-success) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-warning\\/40{\n    --tw-shadow-color: hsl(var(--nextui-warning) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.\\!outline-none{\n    outline: 2px solid transparent !important;\n    outline-offset: 2px !important;\n}\n.outline-none{\n    outline: 2px solid transparent;\n    outline-offset: 2px;\n}\n.ring-1{\n    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-2{\n    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-background{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-ring-opacity)));\n}\n.ring-danger{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-ring-opacity)));\n}\n.ring-default{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-ring-opacity)));\n}\n.ring-focus{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-focus) / var(--nextui-focus-opacity, var(--tw-ring-opacity)));\n}\n.ring-primary{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-ring-opacity)));\n}\n.ring-secondary{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-ring-opacity)));\n}\n.ring-success{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-ring-opacity)));\n}\n.ring-transparent{\n    --tw-ring-color: transparent;\n}\n.ring-warning{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-ring-opacity)));\n}\n.ring-offset-2{\n    --tw-ring-offset-width: 2px;\n}\n.ring-offset-background{\n    --tw-ring-offset-color: hsl(var(--nextui-background) / var(--nextui-background-opacity, 1));\n}\n.blur{\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-lg{\n    --tw-blur: blur(16px);\n    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.saturate-150{\n    --tw-saturate: saturate(1.5);\n    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter{\n    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur{\n    --tw-backdrop-blur: blur(8px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-lg{\n    --tw-backdrop-blur: blur(16px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-md{\n    --tw-backdrop-blur: blur(12px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm{\n    --tw-backdrop-blur: blur(4px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-xl{\n    --tw-backdrop-blur: blur(24px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-opacity-disabled{\n    --tw-backdrop-opacity: opacity(var(--nextui-disabled-opacity));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-saturate-150{\n    --tw-backdrop-saturate: saturate(1.5);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.\\!transition-none{\n    transition-property: none !important;\n}\n.transition{\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.transition-\\[color\\2c opacity\\]{\n    transition-property: color,opacity;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.transition-\\[transform\\2c background-color\\2c color\\]{\n    transition-property: transform,background-color,color;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.transition-\\[transform\\2c color\\2c left\\2c opacity\\]{\n    transition-property: transform,color,left,opacity;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.transition-all{\n    transition-property: all;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.transition-colors{\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.transition-none{\n    transition-property: none;\n}\n.transition-opacity{\n    transition-property: opacity;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.transition-transform{\n    transition-property: transform;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.\\!duration-100{\n    transition-duration: 100ms !important;\n}\n.\\!duration-150{\n    transition-duration: 150ms !important;\n}\n.\\!duration-200{\n    transition-duration: 200ms !important;\n}\n.\\!duration-250{\n    transition-duration: 250ms !important;\n}\n.\\!duration-300{\n    transition-duration: 300ms !important;\n}\n.\\!duration-500{\n    transition-duration: 500ms !important;\n}\n.duration-150{\n    transition-duration: 150ms;\n}\n.\\!ease-out{\n    transition-timing-function: cubic-bezier(0, 0, 0.2, 1) !important;\n}\n.\\!ease-soft-spring{\n    transition-timing-function: cubic-bezier(0.155, 1.105, 0.295, 1.12) !important;\n}\n.will-change-auto{\n    will-change: auto;\n}\n.will-change-transform{\n    will-change: transform;\n}\n:root,.light,[data-theme="light"]{\n    color-scheme: light;\n    --nextui-background: 0 0% 100%;\n    --nextui-foreground-50: 0 0% 98.04%;\n    --nextui-foreground-100: 240 4.76% 95.88%;\n    --nextui-foreground-200: 240 5.88% 90%;\n    --nextui-foreground-300: 240 4.88% 83.92%;\n    --nextui-foreground-400: 240 5.03% 64.9%;\n    --nextui-foreground-500: 240 3.83% 46.08%;\n    --nextui-foreground-600: 240 5.2% 33.92%;\n    --nextui-foreground-700: 240 5.26% 26.08%;\n    --nextui-foreground-800: 240 3.7% 15.88%;\n    --nextui-foreground-900: 240 5.88% 10%;\n    --nextui-foreground: 201.81999999999994 24.44% 8.82%;\n    --nextui-divider: 0 0% 6.67%;\n    --nextui-divider-opacity: 0.15;\n    --nextui-focus: 212.01999999999998 100% 46.67%;\n    --nextui-overlay: 0 0% 0%;\n    --nextui-content1: 0 0% 100%;\n    --nextui-content1-foreground: 201.81999999999994 24.44% 8.82%;\n    --nextui-content2: 240 4.76% 95.88%;\n    --nextui-content2-foreground: 240 3.7% 15.88%;\n    --nextui-content3: 240 5.88% 90%;\n    --nextui-content3-foreground: 240 5.26% 26.08%;\n    --nextui-content4: 240 4.88% 83.92%;\n    --nextui-content4-foreground: 240 5.2% 33.92%;\n    --nextui-default-50: 0 0% 98.04%;\n    --nextui-default-100: 240 4.76% 95.88%;\n    --nextui-default-200: 240 5.88% 90%;\n    --nextui-default-300: 240 4.88% 83.92%;\n    --nextui-default-400: 240 5.03% 64.9%;\n    --nextui-default-500: 240 3.83% 46.08%;\n    --nextui-default-600: 240 5.2% 33.92%;\n    --nextui-default-700: 240 5.26% 26.08%;\n    --nextui-default-800: 240 3.7% 15.88%;\n    --nextui-default-900: 240 5.88% 10%;\n    --nextui-default-foreground: 0 0% 0%;\n    --nextui-default: 240 4.88% 83.92%;\n    --nextui-primary-50: 212.5 92.31% 94.9%;\n    --nextui-primary-100: 211.84000000000003 92.45% 89.61%;\n    --nextui-primary-200: 211.84000000000003 92.45% 79.22%;\n    --nextui-primary-300: 212.24 92.45% 68.82%;\n    --nextui-primary-400: 212.14 92.45% 58.43%;\n    --nextui-primary-500: 212.01999999999998 100% 46.67%;\n    --nextui-primary-600: 212.14 100% 38.43%;\n    --nextui-primary-700: 212.24 100% 28.82%;\n    --nextui-primary-800: 211.84000000000003 100% 19.22%;\n    --nextui-primary-900: 211.84000000000003 100% 9.61%;\n    --nextui-primary-foreground: 0 0% 100%;\n    --nextui-primary: 212.01999999999998 100% 46.67%;\n    --nextui-secondary-50: 270 61.54% 94.9%;\n    --nextui-secondary-100: 270 59.26% 89.41%;\n    --nextui-secondary-200: 270 59.26% 78.82%;\n    --nextui-secondary-300: 270 59.26% 68.24%;\n    --nextui-secondary-400: 270 59.26% 57.65%;\n    --nextui-secondary-500: 270 66.67% 47.06%;\n    --nextui-secondary-600: 270 66.67% 37.65%;\n    --nextui-secondary-700: 270 66.67% 28.24%;\n    --nextui-secondary-800: 270 66.67% 18.82%;\n    --nextui-secondary-900: 270 66.67% 9.41%;\n    --nextui-secondary-foreground: 0 0% 100%;\n    --nextui-secondary: 270 66.67% 47.06%;\n    --nextui-success-50: 146.66999999999996 64.29% 94.51%;\n    --nextui-success-100: 145.71000000000004 61.4% 88.82%;\n    --nextui-success-200: 146.2 61.74% 77.45%;\n    --nextui-success-300: 145.78999999999996 62.57% 66.47%;\n    --nextui-success-400: 146.01 62.45% 55.1%;\n    --nextui-success-500: 145.96000000000004 79.46% 43.92%;\n    --nextui-success-600: 146.01 79.89% 35.1%;\n    --nextui-success-700: 145.78999999999996 79.26% 26.47%;\n    --nextui-success-800: 146.2 79.78% 17.45%;\n    --nextui-success-900: 145.71000000000004 77.78% 8.82%;\n    --nextui-success-foreground: 0 0% 0%;\n    --nextui-success: 145.96000000000004 79.46% 43.92%;\n    --nextui-warning-50: 54.55000000000001 91.67% 95.29%;\n    --nextui-warning-100: 37.139999999999986 91.3% 90.98%;\n    --nextui-warning-200: 37.139999999999986 91.3% 81.96%;\n    --nextui-warning-300: 36.95999999999998 91.24% 73.14%;\n    --nextui-warning-400: 37.00999999999999 91.26% 64.12%;\n    --nextui-warning-500: 37.02999999999997 91.27% 55.1%;\n    --nextui-warning-600: 37.00999999999999 74.22% 44.12%;\n    --nextui-warning-700: 36.95999999999998 73.96% 33.14%;\n    --nextui-warning-800: 37.139999999999986 75% 21.96%;\n    --nextui-warning-900: 37.139999999999986 75% 10.98%;\n    --nextui-warning-foreground: 0 0% 0%;\n    --nextui-warning: 37.02999999999997 91.27% 55.1%;\n    --nextui-danger-50: 339.13 92% 95.1%;\n    --nextui-danger-100: 340 91.84% 90.39%;\n    --nextui-danger-200: 339.3299999999999 90% 80.39%;\n    --nextui-danger-300: 339.11 90.6% 70.78%;\n    --nextui-danger-400: 339 90% 60.78%;\n    --nextui-danger-500: 339.20000000000005 90.36% 51.18%;\n    --nextui-danger-600: 339 86.54% 40.78%;\n    --nextui-danger-700: 339.11 85.99% 30.78%;\n    --nextui-danger-800: 339.3299999999999 86.54% 20.39%;\n    --nextui-danger-900: 340 84.91% 10.39%;\n    --nextui-danger-foreground: 0 0% 100%;\n    --nextui-danger: 339.20000000000005 90.36% 51.18%;\n    --nextui-divider-weight: 1px;\n    --nextui-disabled-opacity: .5;\n    --nextui-font-size-tiny: 0.75rem;\n    --nextui-font-size-small: 0.875rem;\n    --nextui-font-size-medium: 1rem;\n    --nextui-font-size-large: 1.125rem;\n    --nextui-line-height-tiny: 1rem;\n    --nextui-line-height-small: 1.25rem;\n    --nextui-line-height-medium: 1.5rem;\n    --nextui-line-height-large: 1.75rem;\n    --nextui-radius-small: 8px;\n    --nextui-radius-medium: 12px;\n    --nextui-radius-large: 14px;\n    --nextui-border-width-small: 1px;\n    --nextui-border-width-medium: 2px;\n    --nextui-border-width-large: 3px;\n    --nextui-box-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.02), 0px 2px 10px 0px rgb(0 0 0 / 0.06), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --nextui-box-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.03), 0px 2px 30px 0px rgb(0 0 0 / 0.08), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --nextui-box-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.04), 0px 30px 60px 0px rgb(0 0 0 / 0.12), 0px 0px 1px 0px rgb(0 0 0 / 0.3);\n    --nextui-hover-opacity: .8;\n}\n.dark,[data-theme="dark"]{\n    color-scheme: dark;\n    --nextui-background: 0 0% 0%;\n    --nextui-foreground-50: 240 5.88% 10%;\n    --nextui-foreground-100: 240 3.7% 15.88%;\n    --nextui-foreground-200: 240 5.26% 26.08%;\n    --nextui-foreground-300: 240 5.2% 33.92%;\n    --nextui-foreground-400: 240 3.83% 46.08%;\n    --nextui-foreground-500: 240 5.03% 64.9%;\n    --nextui-foreground-600: 240 4.88% 83.92%;\n    --nextui-foreground-700: 240 5.88% 90%;\n    --nextui-foreground-800: 240 4.76% 95.88%;\n    --nextui-foreground-900: 0 0% 98.04%;\n    --nextui-foreground: 210 5.56% 92.94%;\n    --nextui-focus: 212.01999999999998 100% 46.67%;\n    --nextui-overlay: 0 0% 0%;\n    --nextui-divider: 0 0% 100%;\n    --nextui-divider-opacity: 0.15;\n    --nextui-content1: 240 5.88% 10%;\n    --nextui-content1-foreground: 0 0% 98.04%;\n    --nextui-content2: 240 3.7% 15.88%;\n    --nextui-content2-foreground: 240 4.76% 95.88%;\n    --nextui-content3: 240 5.26% 26.08%;\n    --nextui-content3-foreground: 240 5.88% 90%;\n    --nextui-content4: 240 5.2% 33.92%;\n    --nextui-content4-foreground: 240 4.88% 83.92%;\n    --nextui-default-50: 240 5.88% 10%;\n    --nextui-default-100: 240 3.7% 15.88%;\n    --nextui-default-200: 240 5.26% 26.08%;\n    --nextui-default-300: 240 5.2% 33.92%;\n    --nextui-default-400: 240 3.83% 46.08%;\n    --nextui-default-500: 240 5.03% 64.9%;\n    --nextui-default-600: 240 4.88% 83.92%;\n    --nextui-default-700: 240 5.88% 90%;\n    --nextui-default-800: 240 4.76% 95.88%;\n    --nextui-default-900: 0 0% 98.04%;\n    --nextui-default-foreground: 0 0% 100%;\n    --nextui-default: 240 5.26% 26.08%;\n    --nextui-primary-50: 211.84000000000003 100% 9.61%;\n    --nextui-primary-100: 211.84000000000003 100% 19.22%;\n    --nextui-primary-200: 212.24 100% 28.82%;\n    --nextui-primary-300: 212.14 100% 38.43%;\n    --nextui-primary-400: 212.01999999999998 100% 46.67%;\n    --nextui-primary-500: 212.14 92.45% 58.43%;\n    --nextui-primary-600: 212.24 92.45% 68.82%;\n    --nextui-primary-700: 211.84000000000003 92.45% 79.22%;\n    --nextui-primary-800: 211.84000000000003 92.45% 89.61%;\n    --nextui-primary-900: 212.5 92.31% 94.9%;\n    --nextui-primary-foreground: 0 0% 100%;\n    --nextui-primary: 212.01999999999998 100% 46.67%;\n    --nextui-secondary-50: 270 66.67% 9.41%;\n    --nextui-secondary-100: 270 66.67% 18.82%;\n    --nextui-secondary-200: 270 66.67% 28.24%;\n    --nextui-secondary-300: 270 66.67% 37.65%;\n    --nextui-secondary-400: 270 66.67% 47.06%;\n    --nextui-secondary-500: 270 59.26% 57.65%;\n    --nextui-secondary-600: 270 59.26% 68.24%;\n    --nextui-secondary-700: 270 59.26% 78.82%;\n    --nextui-secondary-800: 270 59.26% 89.41%;\n    --nextui-secondary-900: 270 61.54% 94.9%;\n    --nextui-secondary-foreground: 0 0% 100%;\n    --nextui-secondary: 270 59.26% 57.65%;\n    --nextui-success-50: 145.71000000000004 77.78% 8.82%;\n    --nextui-success-100: 146.2 79.78% 17.45%;\n    --nextui-success-200: 145.78999999999996 79.26% 26.47%;\n    --nextui-success-300: 146.01 79.89% 35.1%;\n    --nextui-success-400: 145.96000000000004 79.46% 43.92%;\n    --nextui-success-500: 146.01 62.45% 55.1%;\n    --nextui-success-600: 145.78999999999996 62.57% 66.47%;\n    --nextui-success-700: 146.2 61.74% 77.45%;\n    --nextui-success-800: 145.71000000000004 61.4% 88.82%;\n    --nextui-success-900: 146.66999999999996 64.29% 94.51%;\n    --nextui-success-foreground: 0 0% 0%;\n    --nextui-success: 145.96000000000004 79.46% 43.92%;\n    --nextui-warning-50: 37.139999999999986 75% 10.98%;\n    --nextui-warning-100: 37.139999999999986 75% 21.96%;\n    --nextui-warning-200: 36.95999999999998 73.96% 33.14%;\n    --nextui-warning-300: 37.00999999999999 74.22% 44.12%;\n    --nextui-warning-400: 37.02999999999997 91.27% 55.1%;\n    --nextui-warning-500: 37.00999999999999 91.26% 64.12%;\n    --nextui-warning-600: 36.95999999999998 91.24% 73.14%;\n    --nextui-warning-700: 37.139999999999986 91.3% 81.96%;\n    --nextui-warning-800: 37.139999999999986 91.3% 90.98%;\n    --nextui-warning-900: 54.55000000000001 91.67% 95.29%;\n    --nextui-warning-foreground: 0 0% 0%;\n    --nextui-warning: 37.02999999999997 91.27% 55.1%;\n    --nextui-danger-50: 340 84.91% 10.39%;\n    --nextui-danger-100: 339.3299999999999 86.54% 20.39%;\n    --nextui-danger-200: 339.11 85.99% 30.78%;\n    --nextui-danger-300: 339 86.54% 40.78%;\n    --nextui-danger-400: 339.20000000000005 90.36% 51.18%;\n    --nextui-danger-500: 339 90% 60.78%;\n    --nextui-danger-600: 339.11 90.6% 70.78%;\n    --nextui-danger-700: 339.3299999999999 90% 80.39%;\n    --nextui-danger-800: 340 91.84% 90.39%;\n    --nextui-danger-900: 339.13 92% 95.1%;\n    --nextui-danger-foreground: 0 0% 100%;\n    --nextui-danger: 339.20000000000005 90.36% 51.18%;\n    --nextui-divider-weight: 1px;\n    --nextui-disabled-opacity: .5;\n    --nextui-font-size-tiny: 0.75rem;\n    --nextui-font-size-small: 0.875rem;\n    --nextui-font-size-medium: 1rem;\n    --nextui-font-size-large: 1.125rem;\n    --nextui-line-height-tiny: 1rem;\n    --nextui-line-height-small: 1.25rem;\n    --nextui-line-height-medium: 1.5rem;\n    --nextui-line-height-large: 1.75rem;\n    --nextui-radius-small: 8px;\n    --nextui-radius-medium: 12px;\n    --nextui-radius-large: 14px;\n    --nextui-border-width-small: 1px;\n    --nextui-border-width-medium: 2px;\n    --nextui-border-width-large: 3px;\n    --nextui-box-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.05), 0px 2px 10px 0px rgb(0 0 0 / 0.2), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);\n    --nextui-box-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.06), 0px 2px 30px 0px rgb(0 0 0 / 0.22), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);\n    --nextui-box-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.07), 0px 30px 60px 0px rgb(0 0 0 / 0.26), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);\n    --nextui-hover-opacity: .9;\n}\n.tap-highlight-transparent{\n    -webkit-tap-highlight-color: transparent;\n}\n.transition-background{\n    transition-property: background;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.transition-colors-opacity{\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.transition-height{\n    transition-property: height;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.transition-transform-opacity{\n    transition-property: transform, opacity;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.transition-transform-background{\n    transition-property: transform, background;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.transition-transform-colors{\n    transition-property: transform, color, background, background-color, border-color, text-decoration-color, fill, stroke;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.transition-transform-colors-opacity{\n    transition-property: transform, color, background, background-color, border-color, text-decoration-color, fill, stroke, opacity;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.scrollbar-hide{\n    -ms-overflow-style: none;\n    scrollbar-width: none;\n}\n.scrollbar-hide::-webkit-scrollbar{\n    display: none;\n}\n.\\[--picker-height\\:224px\\]{\n    --picker-height: 224px;\n}\n.\\[--scale-enter\\:100\\%\\]{\n    --scale-enter: 100%;\n}\n.\\[--scale-exit\\:100\\%\\]{\n    --scale-exit: 100%;\n}\n.\\[--scroll-shadow-size\\:100px\\]{\n    --scroll-shadow-size: 100px;\n}\n.\\[--slide-enter\\:0px\\]{\n    --slide-enter: 0px;\n}\n.\\[--slide-exit\\:80px\\]{\n    --slide-exit: 80px;\n}\n.\\[mask-image\\:linear-gradient\\(\\#000\\2c \\#000\\2c transparent_0\\2c \\#000_var\\(--scroll-shadow-size\\)\\2c \\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\2c transparent\\)\\]{\n    mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);\n}\n.file\\:cursor-pointer::file-selector-button{\n    cursor: pointer;\n}\n.file\\:border-0::file-selector-button{\n    border-width: 0px;\n}\n.file\\:bg-transparent::file-selector-button{\n    background-color: transparent;\n}\n.placeholder\\:text-danger::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.placeholder\\:text-foreground-500::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-500) / var(--nextui-foreground-500-opacity, var(--tw-text-opacity)));\n}\n.placeholder\\:text-primary::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.placeholder\\:text-secondary::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.placeholder\\:text-success-600::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));\n}\n.placeholder\\:text-warning-600::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));\n}\n.before\\:absolute::before{\n    content: var(--tw-content);\n    position: absolute;\n}\n.before\\:inset-0::before{\n    content: var(--tw-content);\n    inset: 0px;\n}\n.before\\:z-0::before{\n    content: var(--tw-content);\n    z-index: 0;\n}\n.before\\:z-\\[-1\\]::before{\n    content: var(--tw-content);\n    z-index: -1;\n}\n.before\\:box-border::before{\n    content: var(--tw-content);\n    box-sizing: border-box;\n}\n.before\\:block::before{\n    content: var(--tw-content);\n    display: block;\n}\n.before\\:hidden::before{\n    content: var(--tw-content);\n    display: none;\n}\n.before\\:h-0\\.5::before{\n    content: var(--tw-content);\n    height: 0.125rem;\n}\n.before\\:h-11::before{\n    content: var(--tw-content);\n    height: 2.75rem;\n}\n.before\\:h-2\\.5::before{\n    content: var(--tw-content);\n    height: 0.625rem;\n}\n.before\\:h-px::before{\n    content: var(--tw-content);\n    height: 1px;\n}\n.before\\:w-0::before{\n    content: var(--tw-content);\n    width: 0px;\n}\n.before\\:w-11::before{\n    content: var(--tw-content);\n    width: 2.75rem;\n}\n.before\\:w-2\\.5::before{\n    content: var(--tw-content);\n    width: 0.625rem;\n}\n.before\\:w-6::before{\n    content: var(--tw-content);\n    width: 1.5rem;\n}\n.before\\:-translate-x-full::before{\n    content: var(--tw-content);\n    --tw-translate-x: -100%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.before\\:-translate-y-1::before{\n    content: var(--tw-content);\n    --tw-translate-y: -0.25rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.before\\:rotate-0::before{\n    content: var(--tw-content);\n    --tw-rotate: 0deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.before\\:rotate-45::before{\n    content: var(--tw-content);\n    --tw-rotate: 45deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes shimmer{\n    100%{\n        content: var(--tw-content);\n        transform: translateX(100%);\n    }\n}\n.before\\:animate-\\[shimmer_2s_infinite\\]::before{\n    content: var(--tw-content);\n    animation: shimmer 2s infinite;\n}\n.before\\:animate-none::before{\n    content: var(--tw-content);\n    animation: none;\n}\n.before\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.5\\)\\]::before{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-medium) * 0.5);\n}\n.before\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.6\\)\\]::before{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-medium) * 0.6);\n}\n.before\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.7\\)\\]::before{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-medium) * 0.7);\n}\n.before\\:rounded-full::before{\n    content: var(--tw-content);\n    border-radius: 9999px;\n}\n.before\\:rounded-none::before{\n    content: var(--tw-content);\n    border-radius: 0px;\n}\n.before\\:rounded-sm::before{\n    content: var(--tw-content);\n    border-radius: 0.125rem;\n}\n.before\\:border-2::before{\n    content: var(--tw-content);\n    border-width: 2px;\n}\n.before\\:border-t::before{\n    content: var(--tw-content);\n    border-top-width: 1px;\n}\n.before\\:border-solid::before{\n    content: var(--tw-content);\n    border-style: solid;\n}\n.before\\:border-content4\\/30::before{\n    content: var(--tw-content);\n    border-color: hsl(var(--nextui-content4) / 0.3);\n}\n.before\\:border-danger::before{\n    content: var(--tw-content);\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.before\\:border-default::before{\n    content: var(--tw-content);\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));\n}\n.before\\:bg-content1::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content1) / var(--nextui-content1-opacity, var(--tw-bg-opacity)));\n}\n.before\\:bg-current::before{\n    content: var(--tw-content);\n    background-color: currentColor;\n}\n.before\\:bg-danger::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.before\\:bg-danger\\/20::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-danger) / 0.2);\n}\n.before\\:bg-default\\/60::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-default) / 0.6);\n}\n.before\\:bg-foreground::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.before\\:bg-primary::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.before\\:bg-primary\\/20::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-primary) / 0.2);\n}\n.before\\:bg-secondary::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.before\\:bg-secondary\\/20::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-secondary) / 0.2);\n}\n.before\\:bg-success::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.before\\:bg-success\\/20::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-success) / 0.2);\n}\n.before\\:bg-warning::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.before\\:bg-warning\\/20::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-warning) / 0.2);\n}\n.before\\:bg-gradient-to-r::before{\n    content: var(--tw-content);\n    background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.before\\:from-transparent::before{\n    content: var(--tw-content);\n    --tw-gradient-from: transparent var(--tw-gradient-from-position);\n    --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.before\\:via-content4::before{\n    content: var(--tw-content);\n    --tw-gradient-to: hsl(var(--nextui-content4) / 0)  var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--nextui-content4) / var(--nextui-content4-opacity, 1)) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.before\\:to-transparent::before{\n    content: var(--tw-content);\n    --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.before\\:opacity-0::before{\n    content: var(--tw-content);\n    opacity: 0;\n}\n.before\\:opacity-100::before{\n    content: var(--tw-content);\n    opacity: 1;\n}\n.before\\:shadow-small::before{\n    content: var(--tw-content);\n    --tw-shadow: var(--nextui-box-shadow-small);\n    --tw-shadow-colored: var(--nextui-box-shadow-small);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.before\\:transition-colors::before{\n    content: var(--tw-content);\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.before\\:transition-none::before{\n    content: var(--tw-content);\n    transition-property: none;\n}\n.before\\:transition-transform::before{\n    content: var(--tw-content);\n    transition-property: transform;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.before\\:duration-150::before{\n    content: var(--tw-content);\n    transition-duration: 150ms;\n}\n.before\\:content-\\[\\\'\\\'\\]::before{\n    --tw-content: \'\';\n    content: var(--tw-content);\n}\n.before\\:transition-width::before{\n    content: var(--tw-content);\n    transition-property: width;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.after\\:absolute::after{\n    content: var(--tw-content);\n    position: absolute;\n}\n.after\\:inset-0::after{\n    content: var(--tw-content);\n    inset: 0px;\n}\n.after\\:-bottom-1::after{\n    content: var(--tw-content);\n    bottom: -0.25rem;\n}\n.after\\:-bottom-\\[2px\\]::after{\n    content: var(--tw-content);\n    bottom: -2px;\n}\n.after\\:bottom-0::after{\n    content: var(--tw-content);\n    bottom: 0px;\n}\n.after\\:left-0::after{\n    content: var(--tw-content);\n    left: 0px;\n}\n.after\\:left-1\\/2::after{\n    content: var(--tw-content);\n    left: 50%;\n}\n.after\\:right-0::after{\n    content: var(--tw-content);\n    right: 0px;\n}\n.after\\:top-0::after{\n    content: var(--tw-content);\n    top: 0px;\n}\n.after\\:-z-10::after{\n    content: var(--tw-content);\n    z-index: -10;\n}\n.after\\:z-0::after{\n    content: var(--tw-content);\n    z-index: 0;\n}\n.after\\:ml-0\\.5::after{\n    content: var(--tw-content);\n    margin-left: 0.125rem;\n}\n.after\\:ms-0\\.5::after{\n    content: var(--tw-content);\n    margin-inline-start: 0.125rem;\n}\n.after\\:block::after{\n    content: var(--tw-content);\n    display: block;\n}\n.after\\:h-0::after{\n    content: var(--tw-content);\n    height: 0px;\n}\n.after\\:h-4::after{\n    content: var(--tw-content);\n    height: 1rem;\n}\n.after\\:h-5::after{\n    content: var(--tw-content);\n    height: 1.25rem;\n}\n.after\\:h-\\[2px\\]::after{\n    content: var(--tw-content);\n    height: 2px;\n}\n.after\\:h-divider::after{\n    content: var(--tw-content);\n    height: var(--nextui-divider-weight);\n}\n.after\\:h-full::after{\n    content: var(--tw-content);\n    height: 100%;\n}\n.after\\:h-px::after{\n    content: var(--tw-content);\n    height: 1px;\n}\n.after\\:w-0::after{\n    content: var(--tw-content);\n    width: 0px;\n}\n.after\\:w-4::after{\n    content: var(--tw-content);\n    width: 1rem;\n}\n.after\\:w-5::after{\n    content: var(--tw-content);\n    width: 1.25rem;\n}\n.after\\:w-6::after{\n    content: var(--tw-content);\n    width: 1.5rem;\n}\n.after\\:w-\\[80\\%\\]::after{\n    content: var(--tw-content);\n    width: 80%;\n}\n.after\\:w-full::after{\n    content: var(--tw-content);\n    width: 100%;\n}\n.after\\:origin-center::after{\n    content: var(--tw-content);\n    transform-origin: center;\n}\n.after\\:-translate-x-1\\/2::after{\n    content: var(--tw-content);\n    --tw-translate-x: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.after\\:translate-y-1::after{\n    content: var(--tw-content);\n    --tw-translate-y: 0.25rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.after\\:rotate-0::after{\n    content: var(--tw-content);\n    --tw-rotate: 0deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.after\\:scale-50::after{\n    content: var(--tw-content);\n    --tw-scale-x: .5;\n    --tw-scale-y: .5;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.after\\:rounded-\\[calc\\(theme\\(borderRadius\\.large\\)\\/2\\)\\]::after{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-large) / 2);\n}\n.after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.5\\)\\]::after{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-medium) * 0.5);\n}\n.after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.6\\)\\]::after{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-medium) * 0.6);\n}\n.after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\*0\\.7\\)\\]::after{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-medium) * 0.7);\n}\n.after\\:rounded-\\[calc\\(theme\\(borderRadius\\.medium\\)\\/3\\)\\]::after{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-medium) / 3);\n}\n.after\\:rounded-\\[calc\\(theme\\(borderRadius\\.small\\)\\/3\\)\\]::after{\n    content: var(--tw-content);\n    border-radius: calc(var(--nextui-radius-small) / 3);\n}\n.after\\:rounded-full::after{\n    content: var(--tw-content);\n    border-radius: 9999px;\n}\n.after\\:rounded-none::after{\n    content: var(--tw-content);\n    border-radius: 0px;\n}\n.after\\:rounded-xl::after{\n    content: var(--tw-content);\n    border-radius: 0.75rem;\n}\n.after\\:\\!bg-danger::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity))) !important;\n}\n.after\\:bg-background::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-content1::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content1) / var(--nextui-content1-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-content3::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content3) / var(--nextui-content3-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-current::after{\n    content: var(--tw-content);\n    background-color: currentColor;\n}\n.after\\:bg-danger::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-default::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-default-foreground::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-divider::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-divider) / var(--nextui-divider-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-primary::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-secondary::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-success::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.after\\:bg-warning::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.after\\:text-danger::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.after\\:text-danger-foreground::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.after\\:text-default-foreground::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.after\\:text-primary-foreground::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.after\\:text-secondary-foreground::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.after\\:text-success-foreground::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.after\\:text-warning-foreground::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.after\\:opacity-0::after{\n    content: var(--tw-content);\n    opacity: 0;\n}\n.after\\:opacity-100::after{\n    content: var(--tw-content);\n    opacity: 1;\n}\n.after\\:shadow-\\[0_1px_0px_0_rgba\\(0\\2c 0\\2c 0\\2c 0\\.05\\)\\]::after{\n    content: var(--tw-content);\n    --tw-shadow: 0 1px 0px 0 rgba(0,0,0,0.05);\n    --tw-shadow-colored: 0 1px 0px 0 var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.after\\:shadow-small::after{\n    content: var(--tw-content);\n    --tw-shadow: var(--nextui-box-shadow-small);\n    --tw-shadow-colored: var(--nextui-box-shadow-small);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.after\\:transition-all::after{\n    content: var(--tw-content);\n    transition-property: all;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.after\\:transition-none::after{\n    content: var(--tw-content);\n    transition-property: none;\n}\n.after\\:transition-transform::after{\n    content: var(--tw-content);\n    transition-property: transform;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.after\\:\\!duration-200::after{\n    content: var(--tw-content);\n    transition-duration: 200ms !important;\n}\n.after\\:duration-150::after{\n    content: var(--tw-content);\n    transition-duration: 150ms;\n}\n.after\\:\\!ease-linear::after{\n    content: var(--tw-content);\n    transition-timing-function: linear !important;\n}\n.after\\:content-\\[\\\'\\\'\\]::after{\n    --tw-content: \'\';\n    content: var(--tw-content);\n}\n.after\\:content-\\[\\\'\\*\\\'\\]::after{\n    --tw-content: \'*\';\n    content: var(--tw-content);\n}\n.after\\:transition-background::after{\n    content: var(--tw-content);\n    transition-property: background;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.after\\:transition-width::after{\n    content: var(--tw-content);\n    transition-property: width;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.after\\:transition-height::after{\n    content: var(--tw-content);\n    transition-property: height;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.after\\:transition-transform-opacity::after{\n    content: var(--tw-content);\n    transition-property: transform, opacity;\n    transition-timing-function: ease;\n    transition-duration: 250ms;\n}\n.first\\:-ml-0\\.5:first-child{\n    margin-left: -0.125rem;\n}\n.first\\:mt-2:first-child{\n    margin-top: 0.5rem;\n}\n.first\\:rounded-s-full:first-child{\n    border-start-start-radius: 9999px;\n    border-end-start-radius: 9999px;\n}\n.first\\:rounded-s-large:first-child{\n    border-start-start-radius: var(--nextui-radius-large);\n    border-end-start-radius: var(--nextui-radius-large);\n}\n.first\\:rounded-s-lg:first-child{\n    border-start-start-radius: 0.5rem;\n    border-end-start-radius: 0.5rem;\n}\n.first\\:rounded-s-medium:first-child{\n    border-start-start-radius: var(--nextui-radius-medium);\n    border-end-start-radius: var(--nextui-radius-medium);\n}\n.first\\:rounded-s-none:first-child{\n    border-start-start-radius: 0px;\n    border-end-start-radius: 0px;\n}\n.first\\:rounded-s-small:first-child{\n    border-start-start-radius: var(--nextui-radius-small);\n    border-end-start-radius: var(--nextui-radius-small);\n}\n.first\\:before\\:rounded-s-lg:first-child::before{\n    content: var(--tw-content);\n    border-start-start-radius: 0.5rem;\n    border-end-start-radius: 0.5rem;\n}\n.last\\:rounded-e-full:last-child{\n    border-start-end-radius: 9999px;\n    border-end-end-radius: 9999px;\n}\n.last\\:rounded-e-large:last-child{\n    border-start-end-radius: var(--nextui-radius-large);\n    border-end-end-radius: var(--nextui-radius-large);\n}\n.last\\:rounded-e-lg:last-child{\n    border-start-end-radius: 0.5rem;\n    border-end-end-radius: 0.5rem;\n}\n.last\\:rounded-e-medium:last-child{\n    border-start-end-radius: var(--nextui-radius-medium);\n    border-end-end-radius: var(--nextui-radius-medium);\n}\n.last\\:rounded-e-none:last-child{\n    border-start-end-radius: 0px;\n    border-end-end-radius: 0px;\n}\n.last\\:rounded-e-small:last-child{\n    border-start-end-radius: var(--nextui-radius-small);\n    border-end-end-radius: var(--nextui-radius-small);\n}\n.last\\:before\\:rounded-e-lg:last-child::before{\n    content: var(--tw-content);\n    border-start-end-radius: 0.5rem;\n    border-end-end-radius: 0.5rem;\n}\n.first-of-type\\:rounded-e-none:first-of-type{\n    border-start-end-radius: 0px;\n    border-end-end-radius: 0px;\n}\n.last-of-type\\:rounded-s-none:last-of-type{\n    border-start-start-radius: 0px;\n    border-end-start-radius: 0px;\n}\n.autofill\\:bg-transparent:autofill{\n    background-color: transparent;\n}\n.focus-within\\:border-danger:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:border-default-foreground:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:border-primary:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:border-secondary:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:border-success:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:border-warning:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:bg-danger-50:focus-within{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.focus-within\\:bg-primary-50:focus-within{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));\n}\n.focus-within\\:bg-secondary-50:focus-within{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));\n}\n.focus-within\\:bg-success-50:focus-within{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));\n}\n.focus-within\\:bg-warning-50:focus-within{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));\n}\n.focus-within\\:after\\:w-full:focus-within::after{\n    content: var(--tw-content);\n    width: 100%;\n}\n.hover\\:-translate-x-0:hover{\n    --tw-translate-x: -0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-125:hover{\n    --tw-scale-x: 1.25;\n    --tw-scale-y: 1.25;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:border-default:hover{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));\n}\n.hover\\:border-default-300:hover{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-border-opacity)));\n}\n.hover\\:border-default-400:hover{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-border-opacity)));\n}\n.hover\\:\\!bg-foreground:hover{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity))) !important;\n}\n.hover\\:bg-danger-100:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));\n}\n.hover\\:bg-default-100:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.hover\\:bg-default-200:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));\n}\n.hover\\:bg-primary-100:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-100) / var(--nextui-primary-100-opacity, var(--tw-bg-opacity)));\n}\n.hover\\:bg-secondary-100:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-100) / var(--nextui-secondary-100-opacity, var(--tw-bg-opacity)));\n}\n.hover\\:bg-success-100:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));\n}\n.hover\\:bg-warning-100:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));\n}\n.hover\\:underline:hover{\n    text-decoration-line: underline;\n}\n.hover\\:\\!opacity-100:hover{\n    opacity: 1 !important;\n}\n.hover\\:opacity-100:hover{\n    opacity: 1;\n}\n.hover\\:opacity-80:hover{\n    opacity: 0.8;\n}\n.hover\\:after\\:bg-danger\\/20:hover::after{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-danger) / 0.2);\n}\n.hover\\:after\\:bg-foreground\\/10:hover::after{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-foreground) / 0.1);\n}\n.hover\\:after\\:bg-primary\\/20:hover::after{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-primary) / 0.2);\n}\n.hover\\:after\\:bg-secondary\\/20:hover::after{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-secondary) / 0.2);\n}\n.hover\\:after\\:bg-success\\/20:hover::after{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-success) / 0.2);\n}\n.hover\\:after\\:bg-warning\\/20:hover::after{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-warning) / 0.2);\n}\n.hover\\:after\\:opacity-100:hover::after{\n    content: var(--tw-content);\n    opacity: 1;\n}\n.focus-within\\:hover\\:border-danger:hover:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:hover\\:border-default-foreground:hover:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:hover\\:border-primary:hover:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:hover\\:border-secondary:hover:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:hover\\:border-success:hover:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:hover\\:border-warning:hover:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.focus-within\\:hover\\:bg-default-100:hover:focus-within{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.focus\\:bg-danger-400\\/50:focus{\n    background-color: hsl(var(--nextui-danger-400) / 0.5);\n}\n.focus\\:bg-default-400\\/50:focus{\n    background-color: hsl(var(--nextui-default-400) / 0.5);\n}\n.focus\\:bg-primary-400\\/50:focus{\n    background-color: hsl(var(--nextui-primary-400) / 0.5);\n}\n.focus\\:bg-secondary-400\\/50:focus{\n    background-color: hsl(var(--nextui-secondary-400) / 0.5);\n}\n.focus\\:bg-success-400\\/50:focus{\n    background-color: hsl(var(--nextui-success-400) / 0.5);\n}\n.focus\\:bg-warning-400\\/50:focus{\n    background-color: hsl(var(--nextui-warning-400) / 0.5);\n}\n.focus\\:underline:focus{\n    text-decoration-line: underline;\n}\n.focus\\:shadow-sm:focus{\n    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.focus-visible\\:outline-none:focus-visible{\n    outline: 2px solid transparent;\n    outline-offset: 2px;\n}\n.active\\:bg-default-200:active{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));\n}\n.active\\:bg-default-300:active{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-bg-opacity)));\n}\n.active\\:underline:active{\n    text-decoration-line: underline;\n}\n.active\\:\\!opacity-70:active{\n    opacity: 0.7 !important;\n}\n.active\\:opacity-disabled:active{\n    opacity: var(--nextui-disabled-opacity);\n}\n.group:hover .group-hover\\:block{\n    display: block;\n}\n.group:hover .group-hover\\:hidden{\n    display: none;\n}\n.group:hover .group-hover\\:border-current{\n    border-color: currentColor;\n}\n.group:hover .group-hover\\:text-current{\n    color: currentColor;\n}\n.aria-expanded\\:scale-\\[0\\.97\\][aria-expanded="true"]{\n    --tw-scale-x: 0.97;\n    --tw-scale-y: 0.97;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.aria-expanded\\:opacity-70[aria-expanded="true"]{\n    opacity: 0.7;\n}\n.data-\\[disabled\\=true\\]\\:pointer-events-none[data-disabled="true"]{\n    pointer-events: none;\n}\n.data-\\[loaded\\=true\\]\\:pointer-events-auto[data-loaded="true"]{\n    pointer-events: auto;\n}\n.data-\\[visible\\=true\\]\\:pointer-events-auto[data-visible="true"]{\n    pointer-events: auto;\n}\n.data-\\[focus-visible\\=true\\]\\:z-10[data-focus-visible="true"]{\n    z-index: 10;\n}\n.data-\\[focused\\=true\\]\\:z-10[data-focused="true"]{\n    z-index: 10;\n}\n.data-\\[has-label\\=true\\]\\:mt-\\[calc\\(theme\\(fontSize\\.small\\)_\\+_10px\\)\\][data-has-label="true"]{\n    margin-top: calc(var(--nextui-font-size-small) + 10px);\n}\n.data-\\[has-label\\=true\\]\\:mt-\\[calc\\(theme\\(fontSize\\.small\\)_\\+_12px\\)\\][data-has-label="true"]{\n    margin-top: calc(var(--nextui-font-size-small) + 12px);\n}\n.data-\\[has-label\\=true\\]\\:mt-\\[calc\\(theme\\(fontSize\\.small\\)_\\+_8px\\)\\][data-has-label="true"]{\n    margin-top: calc(var(--nextui-font-size-small) + 8px);\n}\n.data-\\[open\\=true\\]\\:block[data-open="true"]{\n    display: block;\n}\n.data-\\[open\\=true\\]\\:flex[data-open="true"]{\n    display: flex;\n}\n.data-\\[hidden\\=true\\]\\:hidden[data-hidden="true"]{\n    display: none;\n}\n.data-\\[inert\\=true\\]\\:hidden[data-inert="true"]{\n    display: none;\n}\n.data-\\[justify\\=end\\]\\:flex-grow[data-justify="end"]{\n    flex-grow: 1;\n}\n.data-\\[justify\\=start\\]\\:flex-grow[data-justify="start"]{\n    flex-grow: 1;\n}\n.data-\\[justify\\=end\\]\\:basis-0[data-justify="end"]{\n    flex-basis: 0px;\n}\n.data-\\[justify\\=start\\]\\:basis-0[data-justify="start"]{\n    flex-basis: 0px;\n}\n.data-\\[focus-visible\\=true\\]\\:-translate-x-3[data-focus-visible="true"]{\n    --tw-translate-x: -0.75rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[hover\\=true\\]\\:-translate-x-3[data-hover="true"]{\n    --tw-translate-x: -0.75rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[hover\\=true\\]\\:translate-x-0[data-hover="true"]{\n    --tw-translate-x: 0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[before\\=true\\]\\:rotate-180[data-before="true"]{\n    --tw-rotate: 180deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[direction\\=ascending\\]\\:rotate-180[data-direction="ascending"]{\n    --tw-rotate: 180deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[open\\=true\\]\\:-rotate-90[data-open="true"]{\n    --tw-rotate: -90deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[open\\=true\\]\\:rotate-180[data-open="true"]{\n    --tw-rotate: 180deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[invisible\\=true\\]\\:scale-0[data-invisible="true"]{\n    --tw-scale-x: 0;\n    --tw-scale-y: 0;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[pressed\\=true\\]\\:scale-100[data-pressed="true"]{\n    --tw-scale-x: 1;\n    --tw-scale-y: 1;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[pressed\\=true\\]\\:scale-\\[0\\.97\\][data-pressed="true"]{\n    --tw-scale-x: 0.97;\n    --tw-scale-y: 0.97;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[disabled\\=true\\]\\:cursor-default[data-disabled="true"]{\n    cursor: default;\n}\n.data-\\[disabled\\=true\\]\\:cursor-not-allowed[data-disabled="true"]{\n    cursor: not-allowed;\n}\n.data-\\[dragging\\=true\\]\\:cursor-grabbing[data-dragging="true"]{\n    cursor: grabbing;\n}\n.data-\\[readonly\\=true\\]\\:cursor-default[data-readonly="true"]{\n    cursor: default;\n}\n.data-\\[sortable\\=true\\]\\:cursor-pointer[data-sortable="true"]{\n    cursor: pointer;\n}\n.data-\\[unavailable\\=true\\]\\:cursor-default[data-unavailable="true"]{\n    cursor: default;\n}\n.data-\\[visible\\=true\\]\\:cursor-pointer[data-visible="true"]{\n    cursor: pointer;\n}\n.data-\\[orientation\\=horizontal\\]\\:flex-row[data-orientation="horizontal"]{\n    flex-direction: row;\n}\n.data-\\[has-helper\\=true\\]\\:items-start[data-has-helper="true"]{\n    align-items: flex-start;\n}\n.data-\\[justify\\=start\\]\\:justify-start[data-justify="start"]{\n    justify-content: flex-start;\n}\n.data-\\[justify\\=end\\]\\:justify-end[data-justify="end"]{\n    justify-content: flex-end;\n}\n.data-\\[justify\\=center\\]\\:justify-center[data-justify="center"]{\n    justify-content: center;\n}\n.data-\\[loaded\\=true\\]\\:overflow-visible[data-loaded="true"]{\n    overflow: visible;\n}\n.data-\\[has-multiple-rows\\=true\\]\\:rounded-large[data-has-multiple-rows="true"]{\n    border-radius: var(--nextui-radius-large);\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:rounded-full[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    border-radius: 9999px;\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:rounded-full[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    border-radius: 9999px;\n}\n.data-\\[menu-open\\=true\\]\\:border-none[data-menu-open="true"]{\n    border-style: none;\n}\n.data-\\[active\\=true\\]\\:border-danger[data-active="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.data-\\[active\\=true\\]\\:border-default-400[data-active="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-border-opacity)));\n}\n.data-\\[active\\=true\\]\\:border-primary[data-active="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[active\\=true\\]\\:border-secondary[data-active="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[active\\=true\\]\\:border-success[data-active="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.data-\\[active\\=true\\]\\:border-warning[data-active="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.data-\\[focus\\=true\\]\\:border-danger[data-focus="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.data-\\[focus\\=true\\]\\:border-default-foreground[data-focus="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));\n}\n.data-\\[focus\\=true\\]\\:border-primary[data-focus="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[focus\\=true\\]\\:border-secondary[data-focus="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[focus\\=true\\]\\:border-success[data-focus="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.data-\\[focus\\=true\\]\\:border-warning[data-focus="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.data-\\[hover\\=true\\]\\:border-danger[data-hover="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.data-\\[hover\\=true\\]\\:border-default[data-hover="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));\n}\n.data-\\[hover\\=true\\]\\:border-default-400[data-hover="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-border-opacity)));\n}\n.data-\\[hover\\=true\\]\\:border-primary[data-hover="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[hover\\=true\\]\\:border-secondary[data-hover="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[hover\\=true\\]\\:border-success[data-hover="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.data-\\[hover\\=true\\]\\:border-warning[data-hover="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.data-\\[open\\=true\\]\\:border-danger[data-open="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.data-\\[open\\=true\\]\\:border-default-foreground[data-open="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));\n}\n.data-\\[open\\=true\\]\\:border-primary[data-open="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[open\\=true\\]\\:border-secondary[data-open="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[open\\=true\\]\\:border-success[data-open="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.data-\\[open\\=true\\]\\:border-warning[data-open="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.data-\\[active\\=true\\]\\:bg-danger[data-active="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[active\\=true\\]\\:bg-default-400[data-active="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[active\\=true\\]\\:bg-primary[data-active="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[active\\=true\\]\\:bg-secondary[data-active="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[active\\=true\\]\\:bg-success[data-active="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[active\\=true\\]\\:bg-warning[data-active="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:\\!bg-danger[data-hover="true"]{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!bg-danger-100[data-hover="true"]{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!bg-default[data-hover="true"]{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!bg-primary[data-hover="true"]{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!bg-secondary[data-hover="true"]{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!bg-success[data-hover="true"]{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!bg-warning[data-hover="true"]{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:bg-content2[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-danger[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-danger-100[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-danger-50[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-danger\\/20[data-hover="true"]{\n    background-color: hsl(var(--nextui-danger) / 0.2);\n}\n.data-\\[hover\\=true\\]\\:bg-default[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-default-100[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-default-200[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-default-50[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-50) / var(--nextui-default-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-default\\/40[data-hover="true"]{\n    background-color: hsl(var(--nextui-default) / 0.4);\n}\n.data-\\[hover\\=true\\]\\:bg-foreground-200[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground-200) / var(--nextui-foreground-200-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-primary[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-primary-100[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-100) / var(--nextui-primary-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-primary-50[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-primary\\/20[data-hover="true"]{\n    background-color: hsl(var(--nextui-primary) / 0.2);\n}\n.data-\\[hover\\=true\\]\\:bg-secondary[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-secondary-100[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-100) / var(--nextui-secondary-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-secondary-50[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-secondary\\/20[data-hover="true"]{\n    background-color: hsl(var(--nextui-secondary) / 0.2);\n}\n.data-\\[hover\\=true\\]\\:bg-success[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-success-100[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-success-50[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-success\\/20[data-hover="true"]{\n    background-color: hsl(var(--nextui-success) / 0.2);\n}\n.data-\\[hover\\=true\\]\\:bg-transparent[data-hover="true"]{\n    background-color: transparent;\n}\n.data-\\[hover\\=true\\]\\:bg-warning[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-warning-100[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-warning-50[data-hover="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[hover\\=true\\]\\:bg-warning\\/20[data-hover="true"]{\n    background-color: hsl(var(--nextui-warning) / 0.2);\n}\n.data-\\[in-range\\=false\\]\\:bg-default-200[data-in-range="false"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[in-range\\=true\\]\\:bg-background\\/50[data-in-range="true"]{\n    background-color: hsl(var(--nextui-background) / 0.5);\n}\n.data-\\[in-range\\=true\\]\\:bg-danger[data-in-range="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[in-range\\=true\\]\\:bg-foreground[data-in-range="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[in-range\\=true\\]\\:bg-primary[data-in-range="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[in-range\\=true\\]\\:bg-secondary[data-in-range="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[in-range\\=true\\]\\:bg-success[data-in-range="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[in-range\\=true\\]\\:bg-warning[data-in-range="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[loaded\\=true\\]\\:\\!bg-transparent[data-loaded="true"]{\n    background-color: transparent !important;\n}\n.data-\\[selected\\=true\\]\\:bg-danger[data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:bg-default[data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:bg-foreground[data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:bg-primary[data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:bg-secondary[data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:bg-success[data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:bg-warning[data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-danger[data-hover="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-foreground[data-hover="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-primary[data-hover="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-secondary[data-hover="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-success[data-hover="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-warning[data-hover="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:data-\\[outside-month\\=true\\]\\:bg-transparent[data-outside-month="true"][data-range-selection="true"][data-selected="true"]{\n    background-color: transparent;\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-danger[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-primary[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-secondary[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-success[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-warning[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-danger[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-primary[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-secondary[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-success[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:bg-warning[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[has-end-content\\=true\\]\\:pe-1\\.5[data-has-end-content="true"]{\n    padding-inline-end: 0.375rem;\n}\n.data-\\[has-helper\\=true\\]\\:pb-\\[calc\\(theme\\(fontSize\\.tiny\\)_\\+8px\\)\\][data-has-helper="true"]{\n    padding-bottom: calc(var(--nextui-font-size-tiny) + 8px);\n}\n.data-\\[has-helper\\=true\\]\\:pb-\\[calc\\(theme\\(fontSize\\.tiny\\)_\\+_8px\\)\\][data-has-helper="true"]{\n    padding-bottom: calc(var(--nextui-font-size-tiny) + 8px);\n}\n.data-\\[has-start-content\\=true\\]\\:ps-1\\.5[data-has-start-content="true"]{\n    padding-inline-start: 0.375rem;\n}\n.data-\\[has-title\\=true\\]\\:pt-1[data-has-title="true"]{\n    padding-top: 0.25rem;\n}\n.data-\\[active\\=true\\]\\:font-semibold[data-active="true"]{\n    font-weight: 600;\n}\n.data-\\[active\\=true\\]\\:text-danger-foreground[data-active="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[active\\=true\\]\\:text-default-foreground[data-active="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[active\\=true\\]\\:text-primary-foreground[data-active="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[active\\=true\\]\\:text-secondary-foreground[data-active="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[active\\=true\\]\\:text-success-foreground[data-active="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[active\\=true\\]\\:text-warning-foreground[data-active="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[disabled\\=true\\]\\:text-default-300[data-disabled="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-danger-300[data-placeholder="true"][data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-300) / var(--nextui-danger-300-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-foreground-500[data-placeholder="true"][data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-500) / var(--nextui-foreground-500-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-primary-300[data-placeholder="true"][data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-300) / var(--nextui-primary-300-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-secondary-300[data-placeholder="true"][data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-300) / var(--nextui-secondary-300-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-success-400[data-placeholder="true"][data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-400) / var(--nextui-success-400-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:data-\\[placeholder\\=true\\]\\:text-warning-400[data-placeholder="true"][data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-400) / var(--nextui-warning-400-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:text-danger[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:text-foreground[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:text-primary[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:text-secondary[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:text-success-600[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:text-warning-600[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:\\!text-primary-foreground[data-hover="true"]{\n    --tw-text-opacity: 1 !important;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!text-secondary-foreground[data-hover="true"]{\n    --tw-text-opacity: 1 !important;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!text-success-foreground[data-hover="true"]{\n    --tw-text-opacity: 1 !important;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:\\!text-warning-foreground[data-hover="true"]{\n    --tw-text-opacity: 1 !important;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity))) !important;\n}\n.data-\\[hover\\=true\\]\\:text-danger[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-danger-500[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-danger-foreground[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-default-500[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-default-foreground[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-foreground-400[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-400) / var(--nextui-foreground-400-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-foreground-600[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-600) / var(--nextui-foreground-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-primary[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-primary-400[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-400) / var(--nextui-primary-400-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-primary-foreground[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-secondary[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-secondary-400[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-400) / var(--nextui-secondary-400-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-secondary-foreground[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-success[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-success-600[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-success-foreground[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-warning[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-warning-600[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[hover\\=true\\]\\:text-warning-foreground[data-hover="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[invalid\\=true\\]\\:data-\\[editable\\=true\\]\\:text-danger[data-editable="true"][data-invalid="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.data-\\[invalid\\=true\\]\\:text-danger-300[data-invalid="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-300) / var(--nextui-danger-300-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-background[data-hover="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-danger-foreground[data-hover="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-primary-foreground[data-hover="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-secondary-foreground[data-hover="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-success-foreground[data-hover="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-warning-foreground[data-hover="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:data-\\[outside-month\\=true\\]\\:text-default-300[data-outside-month="true"][data-range-selection="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-danger-500[data-range-selection="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-foreground[data-range-selection="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-primary[data-range-selection="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-secondary[data-range-selection="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-600[data-range-selection="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-warning-500[data-range-selection="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-500) / var(--nextui-warning-500-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-background[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-danger-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-primary-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-secondary-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-warning-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-background[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-danger-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-primary-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-secondary-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-warning-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-background[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-danger[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-danger-foreground[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-default-foreground[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-primary[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-primary-foreground[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-secondary[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-secondary-foreground[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-success-600[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-success-foreground[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-warning-600[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selected\\=true\\]\\:text-warning-foreground[data-selected="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[unavailable\\=true\\]\\:text-default-300[data-unavailable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-text-opacity)));\n}\n.data-\\[unavailable\\=true\\]\\:line-through[data-unavailable="true"]{\n    text-decoration-line: line-through;\n}\n.data-\\[disabled\\=true\\]\\:data-\\[outside-month\\=true\\]\\:opacity-0[data-outside-month="true"][data-disabled="true"]{\n    opacity: 0;\n}\n.data-\\[disabled\\=true\\]\\:opacity-30[data-disabled="true"]{\n    opacity: 0.3;\n}\n.data-\\[hover-unselected\\=true\\]\\:opacity-disabled[data-hover-unselected="true"]{\n    opacity: var(--nextui-disabled-opacity);\n}\n.data-\\[hover\\=true\\]\\:opacity-hover[data-hover="true"]{\n    opacity: var(--nextui-hover-opacity);\n}\n.data-\\[in-range\\=true\\]\\:opacity-100[data-in-range="true"]{\n    opacity: 1;\n}\n.data-\\[invisible\\=true\\]\\:opacity-0[data-invisible="true"]{\n    opacity: 0;\n}\n.data-\\[loaded\\=true\\]\\:opacity-100[data-loaded="true"]{\n    opacity: 1;\n}\n.data-\\[moving\\]\\:opacity-100[data-moving]{\n    opacity: 1;\n}\n.data-\\[pressed\\=true\\]\\:opacity-50[data-pressed="true"]{\n    opacity: 0.5;\n}\n.data-\\[pressed\\=true\\]\\:opacity-70[data-pressed="true"]{\n    opacity: 0.7;\n}\n.data-\\[visible\\=true\\]\\:opacity-100[data-visible="true"]{\n    opacity: 1;\n}\n.data-\\[active\\=true\\]\\:shadow-md[data-active="true"]{\n    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.data-\\[hover\\=true\\]\\:shadow-lg[data-hover="true"]{\n    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:shadow-md[data-selection-end="true"][data-selected="true"]{\n    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:shadow-md[data-selection-start="true"][data-selected="true"]{\n    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.data-\\[selected\\=true\\]\\:shadow-md[data-selected="true"]{\n    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.data-\\[selected\\=true\\]\\:shadow-none[data-selected="true"]{\n    --tw-shadow: 0 0 #0000;\n    --tw-shadow-colored: 0 0 #0000;\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.data-\\[active\\=true\\]\\:shadow-danger\\/40[data-active="true"]{\n    --tw-shadow-color: hsl(var(--nextui-danger) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[active\\=true\\]\\:shadow-default\\/50[data-active="true"]{\n    --tw-shadow-color: hsl(var(--nextui-default) / 0.5);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[active\\=true\\]\\:shadow-primary\\/40[data-active="true"]{\n    --tw-shadow-color: hsl(var(--nextui-primary) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[active\\=true\\]\\:shadow-secondary\\/40[data-active="true"]{\n    --tw-shadow-color: hsl(var(--nextui-secondary) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[active\\=true\\]\\:shadow-success\\/40[data-active="true"]{\n    --tw-shadow-color: hsl(var(--nextui-success) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[active\\=true\\]\\:shadow-warning\\/40[data-active="true"]{\n    --tw-shadow-color: hsl(var(--nextui-warning) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[hover\\=true\\]\\:shadow-danger\\/30[data-hover="true"]{\n    --tw-shadow-color: hsl(var(--nextui-danger) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[hover\\=true\\]\\:shadow-default\\/50[data-hover="true"]{\n    --tw-shadow-color: hsl(var(--nextui-default) / 0.5);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[hover\\=true\\]\\:shadow-primary\\/30[data-hover="true"]{\n    --tw-shadow-color: hsl(var(--nextui-primary) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[hover\\=true\\]\\:shadow-secondary\\/30[data-hover="true"]{\n    --tw-shadow-color: hsl(var(--nextui-secondary) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[hover\\=true\\]\\:shadow-success\\/30[data-hover="true"]{\n    --tw-shadow-color: hsl(var(--nextui-success) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[hover\\=true\\]\\:shadow-warning\\/30[data-hover="true"]{\n    --tw-shadow-color: hsl(var(--nextui-warning) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selected\\=true\\]\\:shadow-danger\\/40[data-selected="true"]{\n    --tw-shadow-color: hsl(var(--nextui-danger) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selected\\=true\\]\\:shadow-foreground\\/40[data-selected="true"]{\n    --tw-shadow-color: hsl(var(--nextui-foreground) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selected\\=true\\]\\:shadow-primary\\/40[data-selected="true"]{\n    --tw-shadow-color: hsl(var(--nextui-primary) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selected\\=true\\]\\:shadow-secondary\\/40[data-selected="true"]{\n    --tw-shadow-color: hsl(var(--nextui-secondary) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selected\\=true\\]\\:shadow-success\\/40[data-selected="true"]{\n    --tw-shadow-color: hsl(var(--nextui-success) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selected\\=true\\]\\:shadow-warning\\/40[data-selected="true"]{\n    --tw-shadow-color: hsl(var(--nextui-warning) / 0.4);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[focus-visible\\=true\\]\\:outline-2[data-focus-visible="true"]{\n    outline-width: 2px;\n}\n.data-\\[focus-visible\\=true\\]\\:outline-offset-2[data-focus-visible="true"]{\n    outline-offset: 2px;\n}\n.data-\\[focus-visible\\=true\\]\\:outline-focus[data-focus-visible="true"]{\n    outline-color: hsl(var(--nextui-focus) / var(--nextui-focus-opacity, 1));\n}\n.data-\\[focus-visible\\]\\:outline-danger-foreground[data-focus-visible]{\n    outline-color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, 1));\n}\n.data-\\[focus-visible\\]\\:outline-default-foreground[data-focus-visible]{\n    outline-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, 1));\n}\n.data-\\[focus-visible\\]\\:outline-primary-foreground[data-focus-visible]{\n    outline-color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, 1));\n}\n.data-\\[focus-visible\\]\\:outline-secondary-foreground[data-focus-visible]{\n    outline-color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, 1));\n}\n.data-\\[focus-visible\\]\\:outline-success-foreground[data-focus-visible]{\n    outline-color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, 1));\n}\n.data-\\[focus-visible\\]\\:outline-warning-foreground[data-focus-visible]{\n    outline-color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, 1));\n}\n.data-\\[menu-open\\=true\\]\\:backdrop-blur-xl[data-menu-open="true"]{\n    --tw-backdrop-blur: blur(24px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.data-\\[disabled\\=true\\]\\:transition-none[data-disabled="true"]{\n    transition-property: none;\n}\n.data-\\[hover\\=true\\]\\:transition-colors[data-hover="true"]{\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.data-\\[moving\\=true\\]\\:transition-transform[data-moving="true"]{\n    transition-property: transform;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n.data-\\[hide-scroll\\=true\\]\\:scrollbar-hide[data-hide-scroll="true"]{\n    -ms-overflow-style: none;\n    scrollbar-width: none;\n}\n.data-\\[hide-scroll\\=true\\]\\:scrollbar-hide[data-hide-scroll="true"]::-webkit-scrollbar{\n    display: none;\n}\n.data-\\[top-bottom-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(\\#000\\2c \\#000\\2c transparent_0\\2c \\#000_var\\(--scroll-shadow-size\\)\\2c \\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\2c transparent\\)\\][data-top-bottom-scroll="true"]{\n    mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);\n}\n.data-\\[top-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(0deg\\2c \\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\2c transparent\\)\\][data-top-scroll="true"]{\n    mask-image: linear-gradient(0deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n}\n.data-\\[bottom-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(180deg\\2c \\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\2c transparent\\)\\][data-bottom-scroll="true"]{\n    mask-image: linear-gradient(180deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n}\n.data-\\[left-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(270deg\\2c \\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\2c transparent\\)\\][data-left-scroll="true"]{\n    mask-image: linear-gradient(270deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n}\n.data-\\[right-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(90deg\\2c \\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\2c transparent\\)\\][data-right-scroll="true"]{\n    mask-image: linear-gradient(90deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);\n}\n.data-\\[left-right-scroll\\=true\\]\\:\\[mask-image\\:linear-gradient\\(to_right\\2c \\#000\\2c \\#000\\2c transparent_0\\2c \\#000_var\\(--scroll-shadow-size\\)\\2c \\#000_calc\\(100\\%_-_var\\(--scroll-shadow-size\\)\\)\\2c transparent\\)\\][data-left-right-scroll="true"]{\n    mask-image: linear-gradient(to right,#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);\n}\n.data-\\[placement\\=bottom-end\\]\\:before\\:-top-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\][data-placement="bottom-end"]::before{\n    content: var(--tw-content);\n    top: calc(calc(1.25rem / 4 - 1.5px) * -1);\n}\n.data-\\[placement\\=bottom-end\\]\\:before\\:right-3[data-placement="bottom-end"]::before{\n    content: var(--tw-content);\n    right: 0.75rem;\n}\n.data-\\[placement\\=bottom-start\\]\\:before\\:-top-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\][data-placement="bottom-start"]::before{\n    content: var(--tw-content);\n    top: calc(calc(1.25rem / 4 - 1.5px) * -1);\n}\n.data-\\[placement\\=bottom-start\\]\\:before\\:left-3[data-placement="bottom-start"]::before{\n    content: var(--tw-content);\n    left: 0.75rem;\n}\n.data-\\[placement\\=bottom\\]\\:before\\:-top-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\][data-placement="bottom"]::before{\n    content: var(--tw-content);\n    top: calc(calc(1.25rem / 4 - 1.5px) * -1);\n}\n.data-\\[placement\\=bottom\\]\\:before\\:left-1\\/2[data-placement="bottom"]::before{\n    content: var(--tw-content);\n    left: 50%;\n}\n.data-\\[placement\\=left-end\\]\\:before\\:-right-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\][data-placement="left-end"]::before{\n    content: var(--tw-content);\n    right: calc(calc(1.25rem / 4 - 3px) * -1);\n}\n.data-\\[placement\\=left-end\\]\\:before\\:bottom-1\\/4[data-placement="left-end"]::before{\n    content: var(--tw-content);\n    bottom: 25%;\n}\n.data-\\[placement\\=left-start\\]\\:before\\:-right-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\][data-placement="left-start"]::before{\n    content: var(--tw-content);\n    right: calc(calc(1.25rem / 4 - 3px) * -1);\n}\n.data-\\[placement\\=left-start\\]\\:before\\:top-1\\/4[data-placement="left-start"]::before{\n    content: var(--tw-content);\n    top: 25%;\n}\n.data-\\[placement\\=left\\]\\:before\\:-right-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_2px\\)\\][data-placement="left"]::before{\n    content: var(--tw-content);\n    right: calc(calc(1.25rem / 4 - 2px) * -1);\n}\n.data-\\[placement\\=left\\]\\:before\\:top-1\\/2[data-placement="left"]::before{\n    content: var(--tw-content);\n    top: 50%;\n}\n.data-\\[placement\\=right-end\\]\\:before\\:-left-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\][data-placement="right-end"]::before{\n    content: var(--tw-content);\n    left: calc(calc(1.25rem / 4 - 3px) * -1);\n}\n.data-\\[placement\\=right-end\\]\\:before\\:bottom-1\\/4[data-placement="right-end"]::before{\n    content: var(--tw-content);\n    bottom: 25%;\n}\n.data-\\[placement\\=right-start\\]\\:before\\:-left-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_3px\\)\\][data-placement="right-start"]::before{\n    content: var(--tw-content);\n    left: calc(calc(1.25rem / 4 - 3px) * -1);\n}\n.data-\\[placement\\=right-start\\]\\:before\\:top-1\\/4[data-placement="right-start"]::before{\n    content: var(--tw-content);\n    top: 25%;\n}\n.data-\\[placement\\=right\\]\\:before\\:-left-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_2px\\)\\][data-placement="right"]::before{\n    content: var(--tw-content);\n    left: calc(calc(1.25rem / 4 - 2px) * -1);\n}\n.data-\\[placement\\=right\\]\\:before\\:top-1\\/2[data-placement="right"]::before{\n    content: var(--tw-content);\n    top: 50%;\n}\n.data-\\[placement\\=top-end\\]\\:before\\:-bottom-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\][data-placement="top-end"]::before{\n    content: var(--tw-content);\n    bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);\n}\n.data-\\[placement\\=top-end\\]\\:before\\:right-3[data-placement="top-end"]::before{\n    content: var(--tw-content);\n    right: 0.75rem;\n}\n.data-\\[placement\\=top-start\\]\\:before\\:-bottom-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\][data-placement="top-start"]::before{\n    content: var(--tw-content);\n    bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);\n}\n.data-\\[placement\\=top-start\\]\\:before\\:left-3[data-placement="top-start"]::before{\n    content: var(--tw-content);\n    left: 0.75rem;\n}\n.data-\\[placement\\=top\\]\\:before\\:-bottom-\\[calc\\(theme\\(spacing\\.5\\)\\/4_-_1\\.5px\\)\\][data-placement="top"]::before{\n    content: var(--tw-content);\n    bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);\n}\n.data-\\[placement\\=top\\]\\:before\\:left-1\\/2[data-placement="top"]::before{\n    content: var(--tw-content);\n    left: 50%;\n}\n.data-\\[loaded\\=true\\]\\:before\\:-z-10[data-loaded="true"]::before{\n    content: var(--tw-content);\n    z-index: -10;\n}\n.data-\\[arrow\\=true\\]\\:before\\:block[data-arrow="true"]::before{\n    content: var(--tw-content);\n    display: block;\n}\n.data-\\[outside-month\\=true\\]\\:before\\:hidden[data-outside-month="true"]::before{\n    content: var(--tw-content);\n    display: none;\n}\n.data-\\[placement\\=bottom\\]\\:before\\:-translate-x-1\\/2[data-placement="bottom"]::before{\n    content: var(--tw-content);\n    --tw-translate-x: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[placement\\=left\\]\\:before\\:-translate-y-1\\/2[data-placement="left"]::before{\n    content: var(--tw-content);\n    --tw-translate-y: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[placement\\=right\\]\\:before\\:-translate-y-1\\/2[data-placement="right"]::before{\n    content: var(--tw-content);\n    --tw-translate-y: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[placement\\=top\\]\\:before\\:-translate-x-1\\/2[data-placement="top"]::before{\n    content: var(--tw-content);\n    --tw-translate-x: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[loaded\\=true\\]\\:before\\:animate-none[data-loaded="true"]::before{\n    content: var(--tw-content);\n    animation: none;\n}\n.data-\\[range-end\\=true\\]\\:before\\:rounded-r-full[data-range-end="true"]::before{\n    content: var(--tw-content);\n    border-top-right-radius: 9999px;\n    border-bottom-right-radius: 9999px;\n}\n.data-\\[range-start\\=true\\]\\:before\\:rounded-l-full[data-range-start="true"]::before{\n    content: var(--tw-content);\n    border-top-left-radius: 9999px;\n    border-bottom-left-radius: 9999px;\n}\n.data-\\[selection-end\\=true\\]\\:before\\:rounded-r-full[data-selection-end="true"]::before{\n    content: var(--tw-content);\n    border-top-right-radius: 9999px;\n    border-bottom-right-radius: 9999px;\n}\n.data-\\[selection-start\\=true\\]\\:before\\:rounded-l-full[data-selection-start="true"]::before{\n    content: var(--tw-content);\n    border-top-left-radius: 9999px;\n    border-bottom-left-radius: 9999px;\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-danger-50[data-range-selection="true"][data-selected="true"]::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-foreground\\/10[data-range-selection="true"][data-selected="true"]::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-foreground) / 0.1);\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-primary-50[data-range-selection="true"][data-selected="true"]::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-secondary-50[data-range-selection="true"][data-selected="true"]::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-success-100[data-range-selection="true"][data-selected="true"]::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-warning-100[data-range-selection="true"][data-selected="true"]::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[loaded\\=true\\]\\:before\\:opacity-0[data-loaded="true"]::before{\n    content: var(--tw-content);\n    opacity: 0;\n}\n.data-\\[selected\\=true\\]\\:before\\:opacity-100[data-selected="true"]::before{\n    content: var(--tw-content);\n    opacity: 1;\n}\n.data-\\[focus\\=true\\]\\:after\\:w-full[data-focus="true"]::after{\n    content: var(--tw-content);\n    width: 100%;\n}\n.data-\\[open\\=true\\]\\:after\\:w-full[data-open="true"]::after{\n    content: var(--tw-content);\n    width: 100%;\n}\n.data-\\[dragging\\=true\\]\\:after\\:scale-100[data-dragging="true"]::after{\n    content: var(--tw-content);\n    --tw-scale-x: 1;\n    --tw-scale-y: 1;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[dragging\\=true\\]\\:after\\:scale-80[data-dragging="true"]::after{\n    content: var(--tw-content);\n    --tw-scale-x: 0.8;\n    --tw-scale-y: 0.8;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[selected\\=true\\]\\:after\\:bg-danger[data-selected="true"]::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:after\\:bg-foreground[data-selected="true"]::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:after\\:bg-primary[data-selected="true"]::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:after\\:bg-secondary[data-selected="true"]::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:after\\:bg-success[data-selected="true"]::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selected\\=true\\]\\:after\\:bg-warning[data-selected="true"]::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[loaded\\=true\\]\\:after\\:opacity-0[data-loaded="true"]::after{\n    content: var(--tw-content);\n    opacity: 0;\n}\n.data-\\[selected\\=true\\]\\:after\\:opacity-100[data-selected="true"]::after{\n    content: var(--tw-content);\n    opacity: 1;\n}\n.data-\\[selectable\\=true\\]\\:focus\\:border-danger:focus[data-selectable="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:border-default:focus[data-selectable="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:border-primary:focus[data-selectable="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:border-secondary:focus[data-selectable="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:border-success:focus[data-selectable="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:border-warning:focus[data-selectable="true"]{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.data-\\[invalid\\=true\\]\\:focus\\:bg-danger-400\\/50:focus[data-invalid="true"]{\n    background-color: hsl(var(--nextui-danger-400) / 0.5);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-danger:focus[data-selectable="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-danger\\/20:focus[data-selectable="true"]{\n    background-color: hsl(var(--nextui-danger) / 0.2);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-default:focus[data-selectable="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-default-100:focus[data-selectable="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-default\\/40:focus[data-selectable="true"]{\n    background-color: hsl(var(--nextui-default) / 0.4);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-primary:focus[data-selectable="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-primary\\/20:focus[data-selectable="true"]{\n    background-color: hsl(var(--nextui-primary) / 0.2);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-secondary:focus[data-selectable="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-secondary\\/20:focus[data-selectable="true"]{\n    background-color: hsl(var(--nextui-secondary) / 0.2);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-success:focus[data-selectable="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-success\\/20:focus[data-selectable="true"]{\n    background-color: hsl(var(--nextui-success) / 0.2);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-warning:focus[data-selectable="true"]{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:bg-warning\\/20:focus[data-selectable="true"]{\n    background-color: hsl(var(--nextui-warning) / 0.2);\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-danger:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-default-foreground:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-primary:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-secondary:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-success:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-success-600:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-warning:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.data-\\[editable\\=true\\]\\:focus\\:text-warning-600:focus[data-editable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));\n}\n.data-\\[invalid\\=true\\]\\:data-\\[editable\\=true\\]\\:focus\\:text-danger:focus[data-editable="true"][data-invalid="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-danger:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-danger-foreground:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-default-500:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-default-foreground:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-primary:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-primary-foreground:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-secondary:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-secondary-foreground:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-success:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-success-foreground:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-warning:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:text-warning-foreground:focus[data-selectable="true"]{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.data-\\[selectable\\=true\\]\\:focus\\:shadow-danger\\/30:focus[data-selectable="true"]{\n    --tw-shadow-color: hsl(var(--nextui-danger) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:shadow-default\\/50:focus[data-selectable="true"]{\n    --tw-shadow-color: hsl(var(--nextui-default) / 0.5);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:shadow-primary\\/30:focus[data-selectable="true"]{\n    --tw-shadow-color: hsl(var(--nextui-primary) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:shadow-secondary\\/30:focus[data-selectable="true"]{\n    --tw-shadow-color: hsl(var(--nextui-secondary) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:shadow-success\\/30:focus[data-selectable="true"]{\n    --tw-shadow-color: hsl(var(--nextui-success) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.data-\\[selectable\\=true\\]\\:focus\\:shadow-warning\\/30:focus[data-selectable="true"]{\n    --tw-shadow-color: hsl(var(--nextui-warning) / 0.3);\n    --tw-shadow: var(--tw-shadow-colored);\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:pointer-events-auto{\n    pointer-events: auto;\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:start-0{\n    inset-inline-start: 0px;\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:start-0{\n    inset-inline-start: 0px;\n}\n.group[data-focus-visible="true"] .group-data-\\[focus-visible\\=true\\]\\:z-10{\n    z-index: 10;\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:ml-4{\n    margin-left: 1rem;\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:ml-5{\n    margin-left: 1.25rem;\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:ml-6{\n    margin-left: 1.5rem;\n}\n.group[data-selected][data-pressed] .group-data-\\[selected\\]\\:group-data-\\[pressed\\]\\:ml-3{\n    margin-left: 0.75rem;\n}\n.group[data-selected][data-pressed] .group-data-\\[selected\\]\\:group-data-\\[pressed\\]\\:ml-4{\n    margin-left: 1rem;\n}\n.group[data-selected][data-pressed] .group-data-\\[selected\\]\\:group-data-\\[pressed\\]\\:ml-5{\n    margin-left: 1.25rem;\n}\n.group[data-focus-visible="true"] .group-data-\\[focus-visible\\=true\\]\\:block{\n    display: block;\n}\n.group[data-has-helper="true"] .group-data-\\[has-helper\\=true\\]\\:flex{\n    display: flex;\n}\n.group[data-focus-visible="true"] .group-data-\\[focus-visible\\=true\\]\\:hidden{\n    display: none;\n}\n.group[data-pressed="true"] .group-data-\\[pressed\\=true\\]\\:w-5{\n    width: 1.25rem;\n}\n.group[data-pressed="true"] .group-data-\\[pressed\\=true\\]\\:w-6{\n    width: 1.5rem;\n}\n.group[data-pressed="true"] .group-data-\\[pressed\\=true\\]\\:w-7{\n    width: 1.75rem;\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(100\\%_\\+_theme\\(fontSize\\.small\\)\\/2_\\+_20px\\)\\]{\n    --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 20px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(100\\%_\\+_theme\\(fontSize\\.small\\)\\/2_\\+_24px\\)\\]{\n    --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 24px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(100\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_\\+_16px\\)\\]{\n    --tw-translate-y: calc(calc(100% + var(--nextui-font-size-tiny) / 2 + 16px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_3\\.5px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 3.5px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_4px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 4px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_6px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_6px_-_theme\\(borderWidth\\.medium\\)\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px - var(--nextui-border-width-medium)) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_8px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_8px_-_theme\\(borderWidth\\.medium\\)\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px - var(--nextui-border-width-medium)) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_-_5px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 5px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_-_8px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_-_8px_-_theme\\(borderWidth\\.medium\\)\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px - var(--nextui-border-width-medium)) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(100\\%_\\+_theme\\(fontSize\\.small\\)\\/2_\\+_20px\\)\\]{\n    --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 20px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(100\\%_\\+_theme\\(fontSize\\.small\\)\\/2_\\+_24px\\)\\]{\n    --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 24px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(100\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_\\+_16px\\)\\]{\n    --tw-translate-y: calc(calc(100% + var(--nextui-font-size-tiny) / 2 + 16px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_3\\.5px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 3.5px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_4px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 4px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_6px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_6px_-_theme\\(borderWidth\\.medium\\)\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px - var(--nextui-border-width-medium)) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_8px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.small\\)\\/2_-_8px_-_theme\\(borderWidth\\.medium\\)\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px - var(--nextui-border-width-medium)) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_-_5px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 5px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_-_8px\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:-translate-y-\\[calc\\(50\\%_\\+_theme\\(fontSize\\.tiny\\)\\/2_-_8px_-_theme\\(borderWidth\\.medium\\)\\)\\]{\n    --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px - var(--nextui-border-width-medium)) * -1);\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:translate-x-3{\n    --tw-translate-x: 0.75rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-copied="true"] .group-data-\\[copied\\=true\\]\\:scale-100{\n    --tw-scale-x: 1;\n    --tw-scale-y: 1;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-copied="true"] .group-data-\\[copied\\=true\\]\\:scale-50{\n    --tw-scale-x: .5;\n    --tw-scale-y: .5;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:scale-85{\n    --tw-scale-x: 0.85;\n    --tw-scale-y: 0.85;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:scale-85{\n    --tw-scale-x: 0.85;\n    --tw-scale-y: 0.85;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-pressed="true"] .group-data-\\[pressed\\=true\\]\\:scale-95{\n    --tw-scale-x: .95;\n    --tw-scale-y: .95;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:scale-100{\n    --tw-scale-x: 1;\n    --tw-scale-y: 1;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-disabled="true"] .group-data-\\[disabled\\=true\\]\\:cursor-not-allowed{\n    cursor: not-allowed;\n}\n.group[data-has-multiple-months="true"] .group-data-\\[has-multiple-months\\=true\\]\\:flex-row{\n    flex-direction: row;\n}\n.group[data-has-label="true"] .group-data-\\[has-label\\=true\\]\\:items-start{\n    align-items: flex-start;\n}\n.group[data-has-label="true"] .group-data-\\[has-label\\=true\\]\\:items-end{\n    align-items: flex-end;\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:\\!border-danger{\n    --tw-border-opacity: 1 !important;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity))) !important;\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:border-danger{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:border-default-foreground{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:border-primary{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:border-secondary{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:border-success{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:border-warning{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:border-danger{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:border-danger{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:border-default-500{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-border-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:border-primary{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-border-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:border-secondary{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-border-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:border-success{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:border-warning{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:\\!bg-danger-50{\n    --tw-bg-opacity: 1 !important;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity))) !important;\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:bg-danger-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:bg-default-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:bg-primary-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:bg-secondary-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:bg-success-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:bg-warning-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));\n}\n.group[data-hover-unselected="true"] .group-data-\\[hover-unselected\\=true\\]\\:bg-default-100{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:bg-danger-50{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:bg-danger{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:bg-default-400{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-bg-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:bg-primary{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-bg-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:bg-secondary{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-bg-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:bg-success{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:bg-warning{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.group[data-has-helper="true"] .group-data-\\[has-helper\\=true\\]\\:pt-2{\n    padding-top: 0.5rem;\n}\n.group[data-has-helper="true"] .group-data-\\[has-helper\\=true\\]\\:pt-3{\n    padding-top: 0.75rem;\n}\n.group[data-has-helper="true"] .group-data-\\[has-helper\\=true\\]\\:pt-4{\n    padding-top: 1rem;\n}\n.group[data-has-label="true"] .group-data-\\[has-label\\=true\\]\\:pt-4{\n    padding-top: 1rem;\n}\n.group[data-has-label="true"] .group-data-\\[has-label\\=true\\]\\:pt-5{\n    padding-top: 1.25rem;\n}\n.group[data-disabled="true"] .group-data-\\[disabled\\=true\\]\\:text-foreground-300{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground-300) / var(--nextui-foreground-300-opacity, var(--tw-text-opacity)));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:text-default-600{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-600) / var(--nextui-default-600-opacity, var(--tw-text-opacity)));\n}\n.group[data-filled-within="true"] .group-data-\\[filled-within\\=true\\]\\:text-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:text-default-600{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-600) / var(--nextui-default-600-opacity, var(--tw-text-opacity)));\n}\n.group[data-filled="true"] .group-data-\\[filled\\=true\\]\\:text-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-has-value="true"] .group-data-\\[has-value\\=true\\]\\:text-default-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-has-value="true"] .group-data-\\[has-value\\=true\\]\\:text-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:text-danger{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-danger{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-danger-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-default-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-foreground) / var(--nextui-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-primary{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary) / var(--nextui-primary-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-primary-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-primary-foreground) / var(--nextui-primary-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-secondary{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary) / var(--nextui-secondary-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-secondary-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-secondary-foreground) / var(--nextui-secondary-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-success{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-success-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-warning{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:text-warning-foreground{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.group[data-copied="true"] .group-data-\\[copied\\=true\\]\\:opacity-0{\n    opacity: 0;\n}\n.group[data-copied="true"] .group-data-\\[copied\\=true\\]\\:opacity-100{\n    opacity: 1;\n}\n.group[data-hover="true"] .group-data-\\[hover\\=true\\]\\:opacity-100{\n    opacity: 1;\n}\n.group[data-loaded="true"] .group-data-\\[loaded\\=true\\]\\:opacity-100{\n    opacity: 1;\n}\n.group[data-pressed="true"] .group-data-\\[pressed\\=true\\]\\:opacity-70{\n    opacity: 0.7;\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:opacity-0{\n    opacity: 0;\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:opacity-100{\n    opacity: 1;\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:opacity-60{\n    opacity: 0.6;\n}\n.group[data-focus-visible="true"] .group-data-\\[focus-visible\\=true\\]\\:ring-2{\n    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.group[data-focus-visible="true"] .group-data-\\[focus-visible\\=true\\]\\:ring-focus{\n    --tw-ring-opacity: 1;\n    --tw-ring-color: hsl(var(--nextui-focus) / var(--nextui-focus-opacity, var(--tw-ring-opacity)));\n}\n.group[data-focus-visible="true"] .group-data-\\[focus-visible\\=true\\]\\:ring-offset-2{\n    --tw-ring-offset-width: 2px;\n}\n.group[data-focus-visible="true"] .group-data-\\[focus-visible\\=true\\]\\:ring-offset-background{\n    --tw-ring-offset-color: hsl(var(--nextui-background) / var(--nextui-background-opacity, 1));\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:before\\:-z-10::before{\n    content: var(--tw-content);\n    z-index: -10;\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:before\\:w-full::before{\n    content: var(--tw-content);\n    width: 100%;\n}\n.group[data-open="true"] .group-data-\\[open\\=true\\]\\:before\\:translate-y-px::before{\n    content: var(--tw-content);\n    --tw-translate-y: 1px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-open="true"] .group-data-\\[open\\=true\\]\\:before\\:rotate-45::before{\n    content: var(--tw-content);\n    --tw-rotate: 45deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-middle="true"] .group-data-\\[middle\\=true\\]\\:before\\:rounded-none::before{\n    content: var(--tw-content);\n    border-radius: 0px;\n}\n.group[data-hover="true"] .group-data-\\[hover\\=true\\]\\:before\\:bg-default-100::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:before\\:bg-default-100::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:before\\:opacity-100::before{\n    content: var(--tw-content);\n    opacity: 1;\n}\n.group[data-required="true"] .group-data-\\[required\\=true\\]\\:after\\:ml-0\\.5::after{\n    content: var(--tw-content);\n    margin-left: 0.125rem;\n}\n.group[data-focus="true"] .group-data-\\[focus\\=true\\]\\:after\\:w-full::after{\n    content: var(--tw-content);\n    width: 100%;\n}\n.group[data-open="true"] .group-data-\\[open\\=true\\]\\:after\\:translate-y-0::after{\n    content: var(--tw-content);\n    --tw-translate-y: 0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-open="true"] .group-data-\\[open\\=true\\]\\:after\\:-rotate-45::after{\n    content: var(--tw-content);\n    --tw-rotate: -45deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:after\\:scale-100::after{\n    content: var(--tw-content);\n    --tw-scale-x: 1;\n    --tw-scale-y: 1;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:after\\:bg-danger::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.group[data-required="true"] .group-data-\\[required\\=true\\]\\:after\\:text-danger::after{\n    content: var(--tw-content);\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));\n}\n.group[data-selected="true"] .group-data-\\[selected\\=true\\]\\:after\\:opacity-100::after{\n    content: var(--tw-content);\n    opacity: 1;\n}\n.group[data-required="true"] .group-data-\\[required\\=true\\]\\:after\\:content-\\[\\\'\\*\\\'\\]::after{\n    --tw-content: \'*\';\n    content: var(--tw-content);\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:hover\\:border-danger:hover{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:hover\\:bg-danger-100:hover{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:focus-within\\:hover\\:border-danger:hover:focus-within{\n    --tw-border-opacity: 1;\n    border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));\n}\n.group[data-invalid="true"] .group-data-\\[invalid\\=true\\]\\:focus-within\\:hover\\:bg-danger-50:hover:focus-within{\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.group[aria-selected="false"][data-hover="true"] .group-aria-\\[selected\\=false\\]\\:group-data-\\[hover\\=true\\]\\:before\\:bg-default-100::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.group[aria-selected="false"][data-hover="true"] .group-aria-\\[selected\\=false\\]\\:group-data-\\[hover\\=true\\]\\:before\\:opacity-70::before{\n    content: var(--tw-content);\n    opacity: 0.7;\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:data-\\[selected\\=true\\]\\:before\\:bg-danger\\/20[data-selected="true"]::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-danger) / 0.2);\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:data-\\[selected\\=true\\]\\:before\\:bg-default\\/60[data-selected="true"]::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-default) / 0.6);\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:data-\\[selected\\=true\\]\\:before\\:bg-primary\\/20[data-selected="true"]::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-primary) / 0.2);\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:data-\\[selected\\=true\\]\\:before\\:bg-secondary\\/20[data-selected="true"]::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-secondary) / 0.2);\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:data-\\[selected\\=true\\]\\:before\\:bg-success\\/20[data-selected="true"]::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-success) / 0.2);\n}\n.group[data-odd="true"] .group-data-\\[odd\\=true\\]\\:data-\\[selected\\=true\\]\\:before\\:bg-warning\\/20[data-selected="true"]::before{\n    content: var(--tw-content);\n    background-color: hsl(var(--nextui-warning) / 0.2);\n}\n.peer[data-filled="true"] ~ .peer-data-\\[filled\\=true\\]\\:block{\n    display: block;\n}\n.peer[data-filled="true"] ~ .peer-data-\\[filled\\=true\\]\\:opacity-70{\n    opacity: 0.7;\n}\n@media (prefers-reduced-motion: reduce){\n    .motion-reduce\\:transition-none{\n        transition-property: none;\n    }\n    .motion-reduce\\:after\\:transition-none::after{\n        content: var(--tw-content);\n        transition-property: none;\n    }\n}\n.dark\\:bg-background:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-background) / var(--nextui-background-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:bg-background\\/20:is(.dark *){\n    background-color: hsl(var(--nextui-background) / 0.2);\n}\n.dark\\:bg-content2:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:bg-default:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:bg-transparent:is(.dark *){\n    background-color: transparent;\n}\n.dark\\:text-danger-500:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));\n}\n.dark\\:text-success:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.dark\\:text-warning:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.dark\\:placeholder\\:text-danger-500:is(.dark *)::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));\n}\n.dark\\:placeholder\\:text-success:is(.dark *)::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.dark\\:placeholder\\:text-warning:is(.dark *)::placeholder{\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.dark\\:before\\:via-default-700\\/10:is(.dark *)::before{\n    content: var(--tw-content);\n    --tw-gradient-to: hsl(var(--nextui-default-700) / 0)  var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--nextui-default-700) / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.dark\\:after\\:bg-content2:is(.dark *)::after{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:focus\\:bg-danger-400\\/20:focus:is(.dark *){\n    background-color: hsl(var(--nextui-danger-400) / 0.2);\n}\n.dark\\:focus\\:bg-success-400\\/20:focus:is(.dark *){\n    background-color: hsl(var(--nextui-success-400) / 0.2);\n}\n.dark\\:focus\\:bg-warning-400\\/20:focus:is(.dark *){\n    background-color: hsl(var(--nextui-warning-400) / 0.2);\n}\n.dark\\:data-\\[hover\\=true\\]\\:bg-content2[data-hover="true"]:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[hover\\=true\\]\\:bg-danger-50[data-hover="true"]:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[hover\\=true\\]\\:bg-success-50[data-hover="true"]:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[hover\\=true\\]\\:bg-warning-50[data-hover="true"]:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-danger[data-hover="true"][data-selected="true"]:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-success[data-hover="true"][data-selected="true"]:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:bg-warning[data-hover="true"][data-selected="true"]:is(.dark *){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[hover\\=true\\]\\:text-danger-500[data-hover="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[hover\\=true\\]\\:text-success-500[data-hover="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-500) / var(--nextui-success-500-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[hover\\=true\\]\\:text-warning-500[data-hover="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-500) / var(--nextui-warning-500-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-danger-foreground[data-hover="true"][data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-success-foreground[data-hover="true"][data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[hover\\=true\\]\\:text-warning-foreground[data-hover="true"][data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-500[data-range-selection="true"][data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-500) / var(--nextui-success-500-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[selection-end\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[selection-start\\=true\\]\\:data-\\[range-selection\\=true\\]\\:text-success-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:text-danger-500[data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:text-success[data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:text-warning[data-selected="true"]:is(.dark *){\n    --tw-text-opacity: 1;\n    color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-success-50[data-range-selection="true"][data-selected="true"]:is(.dark *)::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[selected\\=true\\]\\:data-\\[range-selection\\=true\\]\\:before\\:bg-warning-50[data-range-selection="true"][data-selected="true"]:is(.dark *)::before{\n    content: var(--tw-content);\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));\n}\n.dark\\:data-\\[invalid\\=true\\]\\:focus\\:bg-danger-400\\/20:focus[data-invalid="true"]:is(.dark *){\n    background-color: hsl(var(--nextui-danger-400) / 0.2);\n}\n@media (min-width: 640px){\n    .sm\\:mx-0{\n        margin-left: 0px;\n        margin-right: 0px;\n    }\n    .sm\\:mx-6{\n        margin-left: 1.5rem;\n        margin-right: 1.5rem;\n    }\n    .sm\\:my-0{\n        margin-top: 0px;\n        margin-bottom: 0px;\n    }\n    .sm\\:my-16{\n        margin-top: 4rem;\n        margin-bottom: 4rem;\n    }\n    .sm\\:items-start{\n        align-items: flex-start;\n    }\n    .sm\\:items-end{\n        align-items: flex-end;\n    }\n    .sm\\:items-center{\n        align-items: center;\n    }\n    .sm\\:\\[--scale-enter\\:100\\%\\]{\n        --scale-enter: 100%;\n    }\n    .sm\\:\\[--scale-exit\\:103\\%\\]{\n        --scale-exit: 103%;\n    }\n    .sm\\:\\[--slide-enter\\:0px\\]{\n        --slide-enter: 0px;\n    }\n    .sm\\:\\[--slide-exit\\:0px\\]{\n        --slide-exit: 0px;\n    }\n    .sm\\:data-\\[visible\\=true\\]\\:pointer-events-none[data-visible="true"]{\n        pointer-events: none;\n    }\n    .sm\\:data-\\[visible\\=true\\]\\:opacity-0[data-visible="true"]{\n        opacity: 0;\n    }\n    .group[data-hover="true"] .sm\\:group-data-\\[hover\\=true\\]\\:data-\\[visible\\=true\\]\\:pointer-events-auto[data-visible="true"]{\n        pointer-events: auto;\n    }\n    .group[data-hover="true"] .sm\\:group-data-\\[hover\\=true\\]\\:data-\\[visible\\=true\\]\\:opacity-100[data-visible="true"]{\n        opacity: 1;\n    }\n}\n.rtl\\:left-1:where([dir="rtl"], [dir="rtl"] *){\n    left: 0.25rem;\n}\n.rtl\\:left-1\\.5:where([dir="rtl"], [dir="rtl"] *){\n    left: 0.375rem;\n}\n.rtl\\:left-\\[unset\\]:where([dir="rtl"], [dir="rtl"] *){\n    left: unset;\n}\n.rtl\\:right-0:where([dir="rtl"], [dir="rtl"] *){\n    right: 0px;\n}\n.rtl\\:right-1\\.5:where([dir="rtl"], [dir="rtl"] *){\n    right: 0.375rem;\n}\n.rtl\\:right-\\[unset\\]:where([dir="rtl"], [dir="rtl"] *){\n    right: unset;\n}\n.rtl\\:ml-2:where([dir="rtl"], [dir="rtl"] *){\n    margin-left: 0.5rem;\n}\n.rtl\\:ml-\\[unset\\]:where([dir="rtl"], [dir="rtl"] *){\n    margin-left: unset;\n}\n.rtl\\:mr-2:where([dir="rtl"], [dir="rtl"] *){\n    margin-right: 0.5rem;\n}\n.rtl\\:mr-\\[unset\\]:where([dir="rtl"], [dir="rtl"] *){\n    margin-right: unset;\n}\n.rtl\\:origin-top-right:where([dir="rtl"], [dir="rtl"] *){\n    transform-origin: top right;\n}\n.rtl\\:-rotate-180:where([dir="rtl"], [dir="rtl"] *){\n    --tw-rotate: -180deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rtl\\:flex-row-reverse:where([dir="rtl"], [dir="rtl"] *){\n    flex-direction: row-reverse;\n}\n.rtl\\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 1;\n}\n.rtl\\:pl-6:where([dir="rtl"], [dir="rtl"] *){\n    padding-left: 1.5rem;\n}\n.rtl\\:pr-0:where([dir="rtl"], [dir="rtl"] *){\n    padding-right: 0px;\n}\n.rtl\\:text-right:where([dir="rtl"], [dir="rtl"] *){\n    text-align: right;\n}\n.rtl\\:after\\:ml-\\[unset\\]:where([dir="rtl"], [dir="rtl"] *)::after{\n    content: var(--tw-content);\n    margin-left: unset;\n}\n.rtl\\:after\\:mr-0\\.5:where([dir="rtl"], [dir="rtl"] *)::after{\n    content: var(--tw-content);\n    margin-right: 0.125rem;\n}\n.rtl\\:data-\\[focus-visible\\=true\\]\\:translate-x-3[data-focus-visible="true"]:where([dir="rtl"], [dir="rtl"] *){\n    --tw-translate-x: 0.75rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rtl\\:data-\\[hover\\=true\\]\\:translate-x-3[data-hover="true"]:where([dir="rtl"], [dir="rtl"] *){\n    --tw-translate-x: 0.75rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rtl\\:data-\\[open\\=true\\]\\:-rotate-90[data-open="true"]:where([dir="rtl"], [dir="rtl"] *){\n    --tw-rotate: -90deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group[data-selected="true"] .rtl\\:group-data-\\[selected\\=true\\]\\:ml-0:where([dir="rtl"], [dir="rtl"] *){\n    margin-left: 0px;\n}\n.group[data-selected="true"] .rtl\\:group-data-\\[selected\\=true\\]\\:mr-4:where([dir="rtl"], [dir="rtl"] *){\n    margin-right: 1rem;\n}\n.group[data-selected="true"] .rtl\\:group-data-\\[selected\\=true\\]\\:mr-5:where([dir="rtl"], [dir="rtl"] *){\n    margin-right: 1.25rem;\n}\n.group[data-selected="true"] .rtl\\:group-data-\\[selected\\=true\\]\\:mr-6:where([dir="rtl"], [dir="rtl"] *){\n    margin-right: 1.5rem;\n}\n.\\[\\&\\+\\.border-medium\\.border-danger\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\]+.border-medium.border-danger{\n    margin-inline-start: calc(var(--nextui-border-width-medium) * -1);\n}\n.\\[\\&\\+\\.border-medium\\.border-default\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\]+.border-medium.border-default{\n    margin-inline-start: calc(var(--nextui-border-width-medium) * -1);\n}\n.\\[\\&\\+\\.border-medium\\.border-primary\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\]+.border-medium.border-primary{\n    margin-inline-start: calc(var(--nextui-border-width-medium) * -1);\n}\n.\\[\\&\\+\\.border-medium\\.border-secondary\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\]+.border-medium.border-secondary{\n    margin-inline-start: calc(var(--nextui-border-width-medium) * -1);\n}\n.\\[\\&\\+\\.border-medium\\.border-success\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\]+.border-medium.border-success{\n    margin-inline-start: calc(var(--nextui-border-width-medium) * -1);\n}\n.\\[\\&\\+\\.border-medium\\.border-warning\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.medium\\)\\*-1\\)\\]+.border-medium.border-warning{\n    margin-inline-start: calc(var(--nextui-border-width-medium) * -1);\n}\n.\\[\\&\\:not\\(\\:first-child\\)\\:not\\(\\:last-child\\)\\]\\:rounded-none:not(:first-child):not(:last-child){\n    border-radius: 0px;\n}\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:-ml-1:not(:first-child){\n    margin-left: -0.25rem;\n}\n.\\[\\&\\:not\\(\\:first-of-type\\)\\:not\\(\\:last-of-type\\)\\]\\:rounded-none:not(:first-of-type):not(:last-of-type){\n    border-radius: 0px;\n}\n.\\[\\&\\:not\\(\\:first-of-type\\)\\]\\:ms-\\[calc\\(theme\\(borderWidth\\.2\\)\\*-1\\)\\]:not(:first-of-type){\n    margin-inline-start: calc(2px * -1);\n}\n.\\[\\&\\>\\*\\]\\:relative>*{\n    position: relative;\n}\n.\\[\\&\\>svg\\]\\:max-w-\\[theme\\(spacing\\.8\\)\\]>svg{\n    max-width: 2rem;\n}\n.\\[\\&\\>tr\\]\\:first\\:rounded-lg:first-child>tr{\n    border-radius: 0.5rem;\n}\n.\\[\\&\\>tr\\]\\:first\\:shadow-small:first-child>tr{\n    --tw-shadow: var(--nextui-box-shadow-small);\n    --tw-shadow-colored: var(--nextui-box-shadow-small);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.\\[\\&\\[data-hover\\=true\\]\\:not\\(\\[data-active\\=true\\]\\)\\]\\:bg-default-100[data-hover=true]:not([data-active=true]){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));\n}\n.\\[\\&\\[data-hover\\=true\\]\\:not\\(\\[data-active\\=true\\]\\)\\]\\:bg-default-200[data-hover=true]:not([data-active=true]){\n    --tw-bg-opacity: 1;\n    background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));\n}\n.\\[\\&_\\.chevron-icon\\]\\:flex-none .chevron-icon{\n    flex: none;\n}\n.\\[\\&_\\.chevron-icon\\]\\:rotate-180 .chevron-icon{\n    --tw-rotate: 180deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\[\\&_\\.chevron-icon\\]\\:transition-transform .chevron-icon{\n    transition-property: transform;\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    transition-duration: 250ms;\n}\n');M(":root {\n  --chat-ui-background-color: #f5f5f5;\n  --chat-ui-border-color: #d9d9d9;\n  --chat-ui-text-color: #000;\n  --chat-ui-assistant-background-color: #fff;\n  --chat-ui-user-background-color: #daf1da;\n  --chat-ui-input-textarea-bg: #fff;\n  --chat-ui-input-textarea-color: #000;\n  --chat-ui-input-textarea-placeholder-color: #aaa;\n  --chat-ui-copy-button-background-color: #f6f8fa;\n  --chat-ui-copy-button-text-color: rgba(60, 60, 60, 0.33);\n  --chat-ui-copy-button-border-color: #dadde1;\n  --chat-ui-input-section-menu-button-hover-bg: rgba(0, 123, 255, 0.1);\n  --chat-ui-input-container-background-color: #fff;\n  --chat-ui-input-border-radius: 10px;\n  --chat-ui-input-container-border-radius: 6px;\n  --chat-ui-message-bubble-border-radius: 10px;\n  --chat-ui-message-bubble-font-size: 14px;\n  --chat-ui-container-border-radius: 0px;\n  --chat-ui-at-panel-bg: #f8f9fa;\n  --chat-ui-at-panel-border: #e9ecef;\n  --chat-ui-at-panel-border-radius: 8px;\n  --chat-ui-at-panel-item-border-radius: 6px;\n  --chat-ui-at-panel-padding: 8px;\n  --chat-ui-at-panel-item-padding: 8px;\n  --chat-ui-at-panel-item-gap: 4px;\n  --chat-ui-at-panel-scrollbar-track: #f8f9fa;\n  --chat-ui-at-panel-scrollbar-thumb: #ced4da;\n  --chat-ui-at-panel-scrollbar-thumb-hover: #adb5bd;\n  --chat-ui-at-panel-item-hover: #e9ecef;\n  --chat-ui-at-panel-item-selected: #d0ebff;\n  --chat-ui-suggestion-bubble-bg: #edf1f6;\n  --chat-ui-suggestion-bubble-hover-bg: #e2e8f0;\n  --chat-ui-suggestion-text-color: #4b5563;\n  --chat-ui-suggestion-icon-color: #64748b;\n  --chat-ui-copy-button-border-radius: 6px;\n  --chat-ui-send-button-font-size: 16px;\n  --chat-ui-send-button-height: 40px;\n  --chat-ui-send-button-text-color: #fff;\n  --chat-ui-send-button-border-radius: 50%;\n  --chat-ui-send-button-width: 40px;\n  --chat-ui-send-button-margin: 0 0 0 8px;\n  --chat-ui-ref-tag-bg-color: #ccc;\n  --chat-ui-ref-tag-active-bg-color: #0e639c;\n  --chat-ui-ref-tag-active-color: #ffffff;\n  --chat-ui-ref-close-button-color: #ffffff;\n  --chat-ui-ref-expand-overlay-bg: linear-gradient(\n    transparent,\n    rgba(255, 255, 255, 0.8)\n  );\n  --chat-ui-ref-expand-overlay-hover-bg: linear-gradient(\n    transparent,\n    rgba(255, 255, 255, 0.9)\n  );\n  --chat-ui-ref-expand-icon-color: #333333;\n  --chat-ui-ref-shadow-color: rgba(0, 0, 0, 0.1);\n  --chat-ui-ref-bg-color: #1e1e1e;\n  --chat-ui-ref-tag-bg-color: #2d2d2d;\n  --chat-ui-ref-tag-color: #cccccc;\n  --chat-ui-ref-tag-hover-bg-color: #4d4d4d;\n  --chat-ui-ref-tag-active-bg-color: #4d4d4d;\n  --chat-ui-ref-expand-overlay-bg: linear-gradient(\n    transparent,\n    rgba(30, 30, 30, 0.8)\n  );\n  --chat-ui-ref-expand-overlay-hover-bg: linear-gradient(\n    transparent,\n    rgba(30, 30, 30, 0.9)\n  );\n  --chat-ui-ref-expand-icon-color: #cccccc;\n  --chat-ui-ref-shadow-color: rgba(0, 0, 0, 0.15);\n}\n\n.dark {\n  --chat-ui-background-color: #1a1a1a;\n  --chat-ui-border-color: #333;\n  --chat-ui-text-color: #fff;\n  --chat-ui-input-textarea-bg: #252525;\n  --chat-ui-input-textarea-color: #fff;\n  --chat-ui-input-textarea-placeholder-color: #888;\n  --chat-ui-assistant-background-color: #333;\n  --chat-ui-user-background-color: #333;\n  --chat-ui-copy-button-background-color: #3c3c3c;\n  --chat-ui-copy-button-text-color: #cccccc;\n  --chat-ui-copy-button-border-color: #3c3c3c;\n  --chat-ui-input-container-background-color: #1a1a1a;\n  --chat-ui-at-panel-bg: #2c3e50;\n  --chat-ui-at-panel-border: #34495e;\n  --chat-ui-at-panel-scrollbar-track: #2c3e50;\n  --chat-ui-at-panel-scrollbar-thumb: #7f8c8d;\n  --chat-ui-at-panel-scrollbar-thumb-hover: #95a5a6;\n  --chat-ui-at-panel-item-hover: #34495e;\n  --chat-ui-at-panel-item-selected: #2c3e50;\n  --chat-ui-send-button-background-color: #016fee;\n  --chat-ui-suggestion-bubble-bg: #2c3e50;\n  --chat-ui-suggestion-bubble-hover-bg: #4a5568;\n  --chat-ui-suggestion-text-color: #bdc3c7;\n  --chat-ui-suggestion-icon-color: #95a5a6;\n}\n\n.ai-app-chat-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100%;\n  background-color: var(--chat-ui-background-color);\n  color: var(--chat-ui-text-color);\n  transition: width 0.25s ease-in-out;\n}\n\n.ai-app-chat-messages {\n  display: flex;\n  flex-direction: column;\n  flex: 1 1 auto;\n  overflow-y: auto;\n  margin: 0 auto;\n  max-width: 800px;\n  min-width: min(100% - 20px, 800px);\n  width: calc(100% - 20px);\n  margin-bottom: 10px;\n  margin-top: 10px;\n}\n\n.ai-app-chat-input-container {\n  position: sticky;\n  bottom: 0;\n  max-width: 800px;\n  min-width: min(100% - 20px, 800px);\n  width: calc(100% - 20px);\n  display: flex;\n  margin: 0 auto;\n  margin-bottom: 10px;\n  z-index: 10;\n  transition: width 0.25s ease-in-out;\n}");import{useState as Ae,useEffect as Ne,useRef as Le,useCallback as Re}from"react";import De from"classnames";import{Spinner as Ee}from"@nextui-org/react";import{memo as Pe,useEffect as $e,useState as Oe}from"react";import Be from"classnames";import We from"react-markdown";import He from"remark-gfm";import Ue from"remark-math";import qe from"rehype-katex";import{Prism as Ve}from"react-syntax-highlighter";import{vscDarkPlus as Je}from"react-syntax-highlighter/dist/esm/styles/prism";import"katex/dist/katex.min.css";M('@charset "UTF-8";\n.dark .markdown-body {\n  --text-primary: #f7fafc;\n  --text-secondary: #cbd5e0;\n  --bg-primary: none;\n  --bg-secondary: #2d3748;\n  --bg-quote: #2d3748;\n  --border-quote: #4a5568;\n  --link-color: #63b3ed;\n  --link-hover: #90cdf4;\n  --code-bg: #2d3748;\n  --border-color: #4a5568;\n  --heading-color: #f7fafc;\n  --syntax-bg: #1e293b;\n  --syntax-border: #334155;\n  --syntax-highlight: rgba(56, 139, 253, 0.15);\n  color: var(--text-primary);\n  background-color: var(--bg-primary);\n}\n.dark .markdown-body pre {\n  background: var(--syntax-bg) !important;\n  border: 1px solid var(--syntax-border);\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);\n}\n\n.markdown-body {\n  font-size: 16px;\n  line-height: 1.6;\n  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;\n  letter-spacing: -0.011em;\n  --text-primary: #1a202c;\n  --text-secondary: #4a5568;\n  --bg-primary: #ffffff;\n  --bg-secondary: #f8fafc;\n  --bg-quote: #f8fafc;\n  --border-quote: #e2e8f0;\n  --link-color: #3182ce;\n  --link-hover: #2c5282;\n  --code-bg: #f1f5f9;\n  --border-color: #e2e8f0;\n  --heading-color: #1a202c;\n  --syntax-bg: #f8fafc;\n  --syntax-border: #e2e8f0;\n  --syntax-highlight: rgba(56, 139, 253, 0.15);\n  color: var(--text-primary);\n  background-color: var(--bg-primary);\n}\n.markdown-body h1,\n.markdown-body h2,\n.markdown-body h3,\n.markdown-body h4,\n.markdown-body h5,\n.markdown-body h6 {\n  color: var(--heading-color);\n  font-weight: 700;\n  letter-spacing: -0.025em;\n  margin: 2em 0 0.75em;\n  line-height: 1.2;\n  position: relative;\n}\n.markdown-body h1:first-child,\n.markdown-body h2:first-child,\n.markdown-body h3:first-child,\n.markdown-body h4:first-child,\n.markdown-body h5:first-child,\n.markdown-body h6:first-child {\n  margin-top: 0.5em;\n}\n.markdown-body h1::before,\n.markdown-body h2::before,\n.markdown-body h3::before,\n.markdown-body h4::before,\n.markdown-body h5::before,\n.markdown-body h6::before {\n  content: "";\n  position: absolute;\n  left: -0.8em;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: linear-gradient(to bottom, var(--link-color), transparent);\n  border-radius: 3px;\n  opacity: 0;\n  transform: scaleY(0.7);\n  transition: opacity 0.2s, transform 0.2s;\n}\n.markdown-body h1:hover::before,\n.markdown-body h2:hover::before,\n.markdown-body h3:hover::before,\n.markdown-body h4:hover::before,\n.markdown-body h5:hover::before,\n.markdown-body h6:hover::before {\n  opacity: 1;\n  transform: scaleY(1);\n}\n.markdown-body h1 {\n  font-size: 2.25em;\n  border-bottom: 1px solid var(--border-color);\n  padding-bottom: 0.3em;\n  margin-bottom: 1em;\n}\n.markdown-body h2 {\n  font-size: 1.75em;\n  border-bottom: 1px solid var(--border-color);\n  padding-bottom: 0.2em;\n}\n.markdown-body h3 {\n  font-size: 1.4em;\n}\n.markdown-body h4 {\n  font-size: 1.2em;\n}\n.markdown-body h5 {\n  font-size: 1em;\n}\n.markdown-body h6 {\n  font-size: 0.9em;\n  color: var(--text-secondary);\n}\n.markdown-body p {\n  margin: 0 0 0.3em;\n  line-height: 1.5;\n  color: var(--text-secondary);\n}\n.markdown-body a {\n  color: var(--link-color);\n  text-decoration: none;\n  transition: all 0.2s ease;\n  border-bottom: 1px solid rgba(49, 130, 206, 0.2);\n  padding-bottom: 1px;\n}\n.markdown-body a:hover {\n  color: var(--link-hover);\n  border-bottom-color: var(--link-color);\n}\n.markdown-body blockquote {\n  margin: 1.75em 0;\n  padding: 1em 1.5em;\n  border-left: 4px solid var(--link-color);\n  background: var(--bg-quote);\n  border-radius: 0.5rem;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n}\n.markdown-body blockquote p {\n  font-style: italic;\n}\n.markdown-body blockquote p:last-child {\n  margin-bottom: 0;\n}\n.markdown-body blockquote cite {\n  display: block;\n  margin-top: 0.8em;\n  font-size: 0.9em;\n  text-align: right;\n  color: var(--text-secondary);\n  font-style: normal;\n}\n.markdown-body blockquote cite:before {\n  content: "— ";\n}\n.markdown-body code:not([class*=language-]) {\n  padding: 0.2em 0.4em;\n  font-size: 0.875em;\n  background: var(--code-bg);\n  border-radius: 0.375rem;\n  font-family: "SF Mono", Menlo, Monaco, Consolas, monospace;\n  color: #e45649;\n}\n.markdown-body pre {\n  margin: 1.75em 0;\n  padding: 1.25em;\n  border-radius: 0.5rem;\n  background: var(--syntax-bg);\n  overflow: auto;\n  border: 1px solid var(--syntax-border);\n  transition: all 0.3s ease;\n  position: relative;\n}\n.markdown-body pre:hover {\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n.markdown-body pre code {\n  font-family: "SF Mono", Menlo, Monaco, Consolas, monospace;\n  font-size: 0.9em;\n  line-height: 1.6;\n  color: var(--text-primary);\n  display: block;\n  padding: 0;\n  overflow-x: auto;\n}\n.markdown-body pre code .token.comment,\n.markdown-body pre code .token.prolog,\n.markdown-body pre code .token.doctype,\n.markdown-body pre code .token.cdata {\n  color: #8e96a3;\n}\n.markdown-body pre code .token.punctuation {\n  color: #a8b2c0;\n}\n.markdown-body pre code .token.property,\n.markdown-body pre code .token.tag,\n.markdown-body pre code .token.constant,\n.markdown-body pre code .token.symbol {\n  color: #e45649;\n}\n.markdown-body pre code .token.boolean,\n.markdown-body pre code .token.number {\n  color: #986801;\n}\n.markdown-body pre code .token.selector,\n.markdown-body pre code .token.attr-name,\n.markdown-body pre code .token.string,\n.markdown-body pre code .token.char,\n.markdown-body pre code .token.builtin {\n  color: #50a14f;\n}\n.markdown-body pre code .token.operator,\n.markdown-body pre code .token.entity,\n.markdown-body pre code .token.url,\n.markdown-body pre code .language-css .token.string,\n.markdown-body pre code .style .token.string {\n  color: #0184bc;\n}\n.markdown-body pre code .token.atrule,\n.markdown-body pre code .token.attr-value,\n.markdown-body pre code .token.keyword {\n  color: #a626a4;\n}\n.markdown-body pre code .token.function {\n  color: #4078f2;\n}\n.markdown-body pre code .token.regex,\n.markdown-body pre code .token.important,\n.markdown-body pre code .token.variable {\n  color: #e90;\n}\n.markdown-body pre code .token.important,\n.markdown-body pre code .token.bold {\n  font-weight: bold;\n}\n.markdown-body pre code .token.italic {\n  font-style: italic;\n}\n.markdown-body pre::before {\n  content: attr(data-language);\n  position: absolute;\n  top: 0.5rem;\n  right: 1rem;\n  font-size: 0.7em;\n  color: var(--text-secondary);\n  opacity: 0.7;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n.markdown-body table {\n  width: 100%;\n  margin: 2em 0;\n  border-collapse: separate;\n  border-spacing: 0;\n  border-radius: 0.5rem;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.markdown-body table th,\n.markdown-body table td {\n  border: 1px solid var(--border-color);\n  padding: 0.85em 1.25em;\n}\n.markdown-body table th {\n  background: var(--bg-secondary);\n  font-weight: 600;\n  text-align: left;\n}\n.markdown-body table thead tr {\n  background: var(--bg-secondary);\n}\n.markdown-body table tbody tr {\n  transition: background-color 0.2s ease;\n}\n.markdown-body table tbody tr:hover {\n  background-color: var(--bg-secondary);\n}\n.markdown-body table tr:nth-child(even) {\n  background: var(--bg-quote);\n}\n.markdown-body img {\n  max-width: 100%;\n  height: auto;\n  border-radius: 0.5rem;\n  margin: 1.75em 0;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.markdown-body img:hover {\n  transform: scale(1.01);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);\n}\n.markdown-body ul,\n.markdown-body ol {\n  padding-left: 1.5em;\n  margin: 1em 0;\n}\n.markdown-body ul li,\n.markdown-body ol li {\n  position: relative;\n  margin: 0.3em 0;\n  padding-left: 0.5em;\n  color: var(--text-secondary);\n}\n.markdown-body ul {\n  list-style-type: disc;\n}\n.markdown-body ul ul {\n  list-style-type: circle;\n  margin: 0.3em 0 0.3em 0.5em;\n}\n.markdown-body ul ul ul {\n  list-style-type: square;\n}\n.markdown-body ol {\n  list-style-type: decimal;\n  padding-left: 1em;\n}\n.markdown-body ol li {\n  padding-left: 0.2em;\n  margin-bottom: 0.5em;\n}\n.markdown-body ol li::marker {\n  margin-top: 0.2em;\n}\n.markdown-body ol ol {\n  list-style-type: lower-alpha;\n  margin: 0.5em 0 0.3em 0.5em;\n}\n.markdown-body ol ol ol {\n  list-style-type: lower-roman;\n}\n.markdown-body ul li::before,\n.markdown-body ul li:hover::before,\n.markdown-body ol li::before,\n.markdown-body ol li:hover::before {\n  display: none;\n}\n.markdown-body hr {\n  margin: 2.5em 0;\n  border: none;\n  height: 1px;\n  background: linear-gradient(to right, transparent, var(--border-color), transparent);\n}\n.markdown-body dl {\n  margin: 1.5em 0;\n}\n.markdown-body dl dt {\n  font-weight: 600;\n  color: var(--heading-color);\n  margin-top: 1em;\n}\n.markdown-body dl dd {\n  margin-left: 1.5em;\n  color: var(--text-secondary);\n}\n.markdown-body kbd {\n  display: inline-block;\n  padding: 0.2em 0.4em;\n  font-size: 0.85em;\n  font-family: "SF Mono", Menlo, Monaco, Consolas, monospace;\n  line-height: 1;\n  color: var(--text-primary);\n  background-color: var(--bg-secondary);\n  border: 1px solid var(--border-color);\n  border-radius: 0.25em;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);\n  white-space: nowrap;\n}\n.markdown-body mark {\n  background-color: rgba(255, 220, 0, 0.2);\n  padding: 0.1em 0.2em;\n  border-radius: 0.2em;\n}\n.markdown-body .footnote {\n  font-size: 0.85em;\n  vertical-align: super;\n  color: var(--link-color);\n}\n.markdown-body .footnotes {\n  margin-top: 3em;\n  border-top: 1px solid var(--border-color);\n  padding-top: 1em;\n}\n.markdown-body .footnotes ol {\n  font-size: 0.9em;\n  color: var(--text-secondary);\n}\n.markdown-body .footnotes .footnote-backref {\n  margin-left: 0.5em;\n}');import{jsx as Ke}from"react/jsx-runtime";var Ge=({content:t,isDark:e=!1})=>Ke("div",{className:"markdown-body "+(e?"dark":""),children:Ke(We,{remarkPlugins:[He,Ue],rehypePlugins:[qe],components:{code(t){var e=t,{node:n,className:a,children:r}=e,o=d(e,["node","className","children"]);const i=/language-(\w+)/.exec(a||"");return i?Ke(Ve,{language:i[1],style:Je,children:String(r).replace(/\n$/,"")}):Ke("code",s(c({className:a},o),{children:r}))}},children:t})});import{jsx as Ze,jsxs as Qe}from"react/jsx-runtime";var tn=Pe((function({loading:t,message:e,customMessageRender:n,className:a,isDark:r=!1,showAnimation:o=!1,slots:i={}}){var c;const[s,d]=Oe(!1);if($e((()=>{if(null==e?void 0:e.isDeleting){d(!0);const t=setTimeout((()=>{d(!1)}),250);return console.log(e),()=>clearTimeout(t)}}),[e]),t||!e)return Ze("div",{className:Be(a),children:null!=(c=i.customLoading)?c:Ze(Ee,{size:"sm"})});if(n){const t=n(e);if(t)return Ze("div",{className:Be("assistant"===e.role?"is-assistant":"is-user",s?"fade-out":"",a),children:t})}const{showCopyButton:l="plain-text"===e.type&&"assistant"===e.role}=e;return Ze("div",{className:Be("assistant"===e.role?"is-assistant":"is-user",s?"fade-out":"",o?"animate-in":"",a),children:(()=>{var t;const n=[];switch(e.type){case"plain-text":n.push(Ze(Ge,{content:String(e.content||""),isDark:r,smooth:!e.isFinal&&"assistant"===e.role},"plain-text"));break;case"file":n.push(Ze(Pt,{file:e.content,showDeleteIcon:!1},"file"));break;default:return null}return(i.renderCustomMessageActionUI||l)&&n.push(Qe("div",{className:"flex gap-2 items-center mt-2 w-full",children:[null==(t=i.renderCustomMessageActionUI)?void 0:t.call(i,e),l?Ze(N,{getCopyText:()=>e.content}):null]},"copy-button")),n})()})}));import{useCallback as en}from"react";import{IoArrowForward as nn}from"react-icons/io5";M(".ai-app-suggestion-bubble {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  max-width: 300px;\n  width: 100%;\n  margin-bottom: 6px;\n  padding: 8px 12px;\n  background-color: var(--chat-ui-assistant-background-color);\n  border-radius: var(--chat-ui-message-bubble-border-radius);\n  transition: background-color 0.3s ease;\n  cursor: pointer;\n}\n.ai-app-suggestion-bubble:hover {\n  background-color: var(--chat-ui-suggestion-bubble-hover-bg);\n}\n\n.ai-app-suggestion-text {\n  font-size: 14px;\n  color: var(--chat-ui-suggestion-text-color);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-right: 8px;\n}\n\n.ai-app-suggestion-icon {\n  flex-shrink: 0;\n  color: var(--chat-ui-suggestion-icon-color);\n  font-size: 18px;\n}");import{jsx as an,jsxs as rn}from"react/jsx-runtime";var on=({content:t,setInputText:e,onClick:n})=>{const a=en((()=>{e(t),null==n||n(t)}),[t,n,e]);return rn("div",{className:"ai-app-suggestion-bubble",onClick:a,children:[an("span",{className:"ai-app-suggestion-text",children:t}),an(nn,{className:"ai-app-suggestion-icon"})]})};import{Avatar as cn,AvatarIcon as sn}from"@nextui-org/react";import{FaRobot as dn}from"react-icons/fa";M(".ai-app-message-bubble {\n  display: flex;\n  align-items: start;\n  font-size: var(--chat-ui-message-bubble-font-size);\n  flex-direction: row;\n  word-break: break-all;\n  white-space: normal;\n  font-weight: 400;\n  min-width: 100%;\n  gap: 8px;\n  width: 0;\n  flex: 1 1;\n  padding: 4px 10px;\n  margin-bottom: 10px;\n  border-radius: var(--chat-ui-message-bubble-border-radius);\n  position: relative;\n}\n@keyframes fadeInLeft {\n  from {\n    transform: translateX(-30px);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n.ai-app-message-bubble.fade-out {\n  animation: fadeOutLeft 0.25s ease-out forwards;\n}\n@keyframes fadeOutLeft {\n  from {\n    transform: translateX(0);\n    opacity: 1;\n  }\n  to {\n    transform: translateX(-30px);\n    opacity: 0;\n  }\n}\n.ai-app-message-bubble.animate-in {\n  animation: fadeInLeft 0.5s ease-out;\n}");import{jsx as ln,jsxs as un}from"react/jsx-runtime";var pn=t=>{const{messages:e,messageLoading:n,welcomeMessage:a,onMessageSend:r,slots:o,customClassNames:i,customStyles:c,customMessageRender:s,messageEndRef:d,isDark:l,setInputText:u,suggestions:p,onSuggestionClick:w,avatar:g={user:!0},autoScrollToBottom:v=!1,isUserScrollingRef:x}=t,[b,y]=Ae(15),[h,f]=Ae(!0),m=Le(null),k=Le(null),z=Le(null),_=Le(null),X=ln(dn,{width:22,height:22,size:22,style:{marginTop:8,marginLeft:4,color:"#4A90E2"}}),Y=ln("div",{className:"flex justify-center items-center",style:{width:22,height:22,borderRadius:"50%",marginTop:"1px"},children:ln(sn,{})}),S=Re((()=>{const t=_.current,n=(null==t?void 0:t.scrollHeight)||0,a=(null==t?void 0:t.scrollTop)||0;y((t=>Math.min(t+15,e.length))),requestAnimationFrame((()=>{if(t){const e=t.scrollHeight;h?(t.scrollTop=e,f(!1)):t.scrollTop=a+(e-n)}}))}),[e.length,h]),j=Re((()=>{y(15)}),[]);Ne((()=>{const t={root:_.current,rootMargin:"400px 0px 0px 0px",threshold:0};return m.current=new IntersectionObserver((t=>{t.forEach((t=>{t.isIntersecting&&(t.target===k.current?S():t.target===z.current&&j())}))}),t),k.current&&m.current.observe(k.current),z.current&&m.current.observe(z.current),()=>{m.current&&m.current.disconnect()}}),[S,j]);const C=e.slice(-b).filter(Boolean);Ne((()=>{d.current&&v&&d.current.scrollIntoView({behavior:"smooth"})}),[]),Ne((()=>{const t=_.current;if(!t)return;const e=()=>{const e=Math.abs(t.scrollHeight-t.scrollTop-t.clientHeight)<1;x.current=!e};return t.addEventListener("scroll",e),()=>{t.removeEventListener("scroll",e)}}),[x]);const F=(t,e)=>e?ln(cn,{src:e,radius:"sm",style:{width:"20px",height:"20px"}}):"assistant"===t?(null==g?void 0:g.assistant)?!0===g.assistant?X:g.assistant:null:"user"===t?(null==g?void 0:g.user)?!0===g.user?Y:g.user:null:void 0;return ln("div",{ref:_,className:De("ai-app-chat-messages",i.messageList),style:c.messageList,children:un("div",{style:{maxWidth:"800px",minWidth:"min(100%, 800px)",margin:"0 auto"},children:[ln("div",{ref:k}),a?un("div",{className:"ai-app-message-bubble mb-2",children:[ln("div",{className:"relative",children:F("assistant",a.avatar)}),ln(tn,{message:a,customMessageRender:s,setInputText:u,slots:o})]}):null,C.map(((t,n)=>un("div",{style:c.message,children:[o.beforeMessage,un("div",{className:"min-w-full ai-app-message-bubble",children:[F(t.role,t.avatar),ln(tn,{message:t,sendMessage:r,className:`w-0 flex-1 ${i.message}`,customMessageRender:s,isDark:l,setInputText:u,slots:o,showAnimation:e.findIndex((e=>e===t))>=C.length-15})]}),o.afterMessage]},`${t.role}-${t.timestamp||JSON.stringify(t.content)}_${n}`))),n?ln(tn,{className:"mx-2",loading:!0,setInputText:u,slots:o}):null,null==p?void 0:p.map((t=>ln(on,{content:t,setInputText:u,onClick:w},t))),ln("div",{ref:d}),ln("div",{ref:z})]})})};import{jsx as wn,jsxs as gn}from"react/jsx-runtime";var vn=je(Fe(((t,e)=>{const{states:n=x,onMessageSend:a,onMessageAbort:r,styles:o,classNames:i={},storageDbName:s,customMessageStorage:d,disableInput:l=!1,slots:u={},conversationId:p="default",features:g,customMessageRender:v,welcomeMessage:b,commandPanelConfig:y,inputPlaceholder:h,onCommandTrigger:f,onCommandSelect:m,onCommandDelete:k,isDark:_,onSuggestionClick:X,onClearConversationHistory:Y,suggestions:S=[],avatar:j}=t,C=Se((()=>d||(s?new w(s):void 0)),[d,s]),{messages:F,messageLoading:M,clearMessages:I,addMessage:T,setInputText:A}=z({states:n,conversationId:p,customMessageStorage:C()}),[N]=Te(n.messageEndRefAtom),[L]=Te(n.isUserScrollingRefAtom),R=Ce(null),D=Ce(null);Me(e,(()=>({triggerCommand:t=>{var e;null==(e=D.current)||e.triggerCommand(t)},focusInput:()=>{var t;null==(t=D.current)||t.focus()},getInputTextArea:()=>{var t;return null==(t=D.current)?void 0:t.getInputTextArea()}})));const E=Ye((()=>({container:null==o?void 0:o.container,message:null==o?void 0:o.message,input:null==o?void 0:o.input,inputContainer:null==o?void 0:o.inputContainer,messageList:null==o?void 0:o.messageList})),[o]),P=Ye((()=>({container:i.container,message:i.message,input:i.input,inputContainer:i.inputContainer,messageList:i.messageList})),[i]),$=Se((t=>null==v?void 0:v(t)),[v]),O=Ye((()=>F.filter((t=>"user"===t.role)).filter((t=>"plain-text"===t.type))),[F.length]);return gn("div",{className:Ie("ai-app-chat-container",P.container),ref:R,style:c({},E.container),children:[u.beforeMessageList,wn(pn,{messages:F,messageLoading:M,welcomeMessage:b,onMessageSend:a,slots:u,customClassNames:P,customStyles:E,customMessageRender:$,messageEndRef:N,isDark:_,setInputText:A,suggestions:S,avatar:j,onSuggestionClick:X,isUserScrollingRef:L}),u.afterMessageList,u.beforeInputContainer,wn("div",{className:Ie("ai-app-chat-input-container",P.inputContainer),style:E.inputContainer,children:wn(Xe,{ref:D,disableInput:l,userMessages:O,sendMessage:a,addMessage:T,slots:u,style:E.input,features:g,clearMessages:I,chatStates:n,commandPanelConfig:y,inputPlaceholder:h,abortMessage:r,onCommandTrigger:f,onCommandSelect:m,onCommandDelete:k,onClearConversationHistory:Y})}),u.afterInputContainer]})})));export{vn as ChatUI,ae as CommandPanel,N as CopyButton,w as DefaultMessageStorage,ie as FigmaIcon,Pt as FileItemPreview,$t as FilePreview,m as InputFileType,Ge as MarkdownRenderer,h as MessageRole,f as MessageType,v as createChatStateAtoms,gt as getFileIcon,pt as getFileType,zt as handleFileInput,kt as handleImage,bt as normalizeFiles,xt as normalizeFilesPrompt,Bt as parseCommandRules,z as useChat};
