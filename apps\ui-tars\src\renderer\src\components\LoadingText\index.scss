/**
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */
.loading-shimmer {
  background-position: -100% top;
  opacity: 1;
  will-change: auto;

  text-fill-color: transparent;
  -webkit-text-fill-color: transparent;
  animation-delay: .5s;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-name: loading-shimmer;
  background: var(--text-quaternary) gradient(linear, 100% 0, 0 0, from(var(--text-quaternary)), color-stop(.5, var(--text-primary)), to(var(--text-quaternary)));
  background: var(--text-quaternary) -webkit-gradient(linear, 100% 0, 0 0, from(var(--text-quaternary)), color-stop(.5, var(--text-primary)), to(var(--text-quaternary)));
  background-clip: text;
  -webkit-background-clip: text;
  background-repeat: no-repeat;
  background-size: 50% 200%;
  display: inline-block;
}

@keyframes loading-shimmer {
  0% {
    background-position: -100% top;
  }
  100% {
    background-position: 250% top;
  }
}
