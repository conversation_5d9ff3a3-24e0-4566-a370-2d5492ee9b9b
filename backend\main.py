"""
MVP 主应用程序
FastAPI 后端服务，集成 CodeAct + Docker 混合执行引擎
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 导入我们的核心组件
from core.lightweight_execution_engine import LightweightExecutionEngine
from core.event_manager import EventManager, EventType

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="统一智能体平台 MVP",
    description="CodeAct + Docker 混合执行引擎",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
execution_engine: Optional[LightweightExecutionEngine] = None
event_manager: Optional[EventManager] = None
active_websockets: Dict[str, WebSocket] = {}


class TaskRequest(BaseModel):
    """任务请求模型"""
    user_input: str
    session_id: Optional[str] = None
    options: Dict[str, Any] = {}


class TaskResponse(BaseModel):
    """任务响应模型"""
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    session_id: str
    execution_time: float


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global execution_engine, event_manager
    
    logger.info("启动统一智能体平台 MVP...")
    
    try:
        # 初始化事件管理器
        event_manager = EventManager()
        
        # 初始化模型（这里需要配置实际的模型）
        from langchain_anthropic import ChatAnthropic
        model = ChatAnthropic(
            model="claude-3-sonnet-20240229",
            temperature=0.1
        )
        
        # 初始化沙盒管理器（使用现有的）
        class MockSandboxManager:
            """临时的模拟沙盒管理器"""
            async def get_or_create_sandbox(self):
                # 这里应该返回实际的沙盒实例
                # 暂时返回模拟对象
                return type('MockSandbox', (), {
                    'execute_command': lambda cmd: {"output": f"模拟执行: {cmd}", "exit_code": 0}
                })()
        
        sandbox_manager = MockSandboxManager()
        
        # 初始化执行引擎
        execution_engine = LightweightExecutionEngine(
            sandbox_manager=sandbox_manager,
            model=model
        )
        
        logger.info("系统初始化完成")
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        raise


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "统一智能体平台 MVP",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "execution_engine": execution_engine is not None,
            "event_manager": event_manager is not None
        }
    }


@app.post("/api/execute", response_model=TaskResponse)
async def execute_task(request: TaskRequest):
    """执行任务"""
    if not execution_engine:
        raise HTTPException(status_code=500, detail="执行引擎未初始化")
    
    start_time = datetime.now()
    session_id = request.session_id or f"session_{start_time.timestamp()}"
    
    try:
        logger.info(f"执行任务: {request.user_input[:100]}...")
        
        # 执行任务
        result = await execution_engine.process_user_request(
            user_input=request.user_input,
            session_id=session_id
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return TaskResponse(
            success=result["success"],
            result=result if result["success"] else None,
            error=result.get("error") if not result["success"] else None,
            session_id=session_id,
            execution_time=execution_time
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"任务执行失败: {e}")
        
        return TaskResponse(
            success=False,
            error=str(e),
            session_id=session_id,
            execution_time=execution_time
        )


@app.get("/api/sessions/{session_id}/events")
async def get_session_events(session_id: str, limit: int = 50):
    """获取会话事件"""
    if not event_manager:
        raise HTTPException(status_code=500, detail="事件管理器未初始化")
    
    try:
        events = await event_manager.get_session_events(session_id, limit=limit)
        return {
            "session_id": session_id,
            "events": [event.to_dict() for event in events],
            "count": len(events)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/sessions/{session_id}/timeline")
async def get_session_timeline(session_id: str):
    """获取会话时间线"""
    if not event_manager:
        raise HTTPException(status_code=500, detail="事件管理器未初始化")
    
    try:
        timeline = await event_manager.get_execution_timeline(session_id)
        return timeline
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/stats")
async def get_system_stats():
    """获取系统统计信息"""
    stats = {}
    
    if execution_engine:
        stats["execution"] = execution_engine.get_stats()
    
    if event_manager:
        stats["events"] = event_manager.get_stats()
    
    stats["websockets"] = {
        "active_connections": len(active_websockets)
    }
    
    return stats


@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket连接，用于实时事件推送"""
    await websocket.accept()
    active_websockets[session_id] = websocket
    
    logger.info(f"WebSocket连接建立: {session_id}")
    
    try:
        # 订阅实时事件
        if event_manager:
            def event_callback(event):
                """事件回调函数"""
                try:
                    asyncio.create_task(websocket.send_json(event.to_dict()))
                except Exception as e:
                    logger.error(f"WebSocket发送事件失败: {e}")
            
            subscription_id = event_manager.subscribe_to_real_time_events(
                session_id, event_callback
            )
        
        # 保持连接
        while True:
            try:
                # 接收客户端消息（心跳等）
                message = await websocket.receive_text()
                
                # 处理心跳
                if message == "ping":
                    await websocket.send_text("pong")
                
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
    finally:
        # 清理连接
        if session_id in active_websockets:
            del active_websockets[session_id]
        
        # 取消事件订阅
        if event_manager and 'subscription_id' in locals():
            event_manager.unsubscribe_from_real_time_events(session_id, subscription_id)
        
        logger.info(f"WebSocket连接关闭: {session_id}")


@app.post("/api/analyze-intent")
async def analyze_intent(request: TaskRequest):
    """分析用户意图"""
    if not execution_engine:
        raise HTTPException(status_code=500, detail="执行引擎未初始化")
    
    try:
        intent = execution_engine.task_router.analyze_user_intent(request.user_input)
        return {
            "user_input": request.user_input,
            "intent": intent,
            "recommendation": execution_engine.task_router.get_execution_recommendation(request.user_input)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 开发模式下的测试端点
@app.post("/api/test/codeact")
async def test_codeact():
    """测试CodeAct功能"""
    if not execution_engine:
        raise HTTPException(status_code=500, detail="执行引擎未初始化")
    
    test_code = """
import pandas as pd
import numpy as np

# 创建测试数据
data = {
    'x': range(10),
    'y': np.random.randn(10)
}
df = pd.DataFrame(data)

print("数据创建完成:")
print(df.head())
print(f"数据形状: {df.shape}")

# 计算统计信息
mean_y = df['y'].mean()
print(f"Y的平均值: {mean_y}")
"""
    
    try:
        output, variables = await execution_engine._execute_python_safely(test_code, {})
        return {
            "test": "轻量级Python执行",
            "code": test_code,
            "output": output,
            "variables": list(variables.keys()) if variables else []
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
