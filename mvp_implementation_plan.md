# 🚀 MVP实施计划 - CodeAct + Docker混合架构

## 🎯 修订后的MVP开发计划

### 核心策略调整
基于对CodeAct和Suna Docker沙盒的深入分析，我们采用**智能混合执行架构**：
- **CodeAct作为主要Agent大脑** - 处理80%的智能任务
- **Daytona Docker作为特殊工具** - 处理需要完整系统环境的复杂任务
- **智能路由系统** - 自动选择最适合的执行环境

---

## 📋 阶段1：核心架构 + CodeAct集成 (3周)

### 🎯 目标
构建智能Agent大脑和混合执行环境，实现端到端的用户价值验证

### 📦 具体交付物

#### 1.1 CodeAct Agent集成 (Week 1)
```
✅ LangGraph CodeAct框架集成
✅ 自定义Daytona Docker eval_fn实现
✅ Python代码执行环境配置
✅ 基础工具集成（文件操作、API调用）
```

#### 1.2 智能路由系统 (Week 1-2)
```
✅ 任务意图分析器
✅ 执行环境选择逻辑
✅ Python vs Docker智能路由
✅ 错误处理和回退机制
```

#### 1.3 基础Web界面 (Week 2-3)
```
✅ Next.js前端框架搭建
✅ 聊天界面实现
✅ 实时执行状态展示
✅ 基础可视化组件
```

#### 1.4 端到端验证 (Week 3)
```
✅ 核心测试用例通过
✅ 用户流程完整验证
✅ 性能基准测试
✅ 错误处理验证
```

### 🔧 技术实现方案

#### 混合执行引擎架构
```python
# backend/core/execution_engine.py
class UnifiedExecutionEngine:
    def __init__(self, sandbox_manager, model):
        self.sandbox_manager = sandbox_manager
        self.model = model
        self.task_router = TaskRouter()
        
        # 创建CodeAct Agent
        self.codeact_agent = create_codeact(
            model=model,
            tools=self._get_python_tools(),
            eval_fn=self._hybrid_eval_fn
        )
    
    async def process_user_request(self, user_input: str) -> dict:
        """处理用户请求的主入口"""
        # 1. 分析用户意图
        intent = self.task_router.analyze_user_intent(user_input)
        
        # 2. 选择执行策略
        if intent["execution_env"] == "docker_sandbox":
            return await self._execute_docker_task(user_input, intent)
        else:
            return await self._execute_codeact_task(user_input, intent)
    
    async def _hybrid_eval_fn(self, code: str, context: dict) -> tuple[str, dict]:
        """智能混合执行函数"""
        execution_type = self._analyze_code_requirements(code)
        
        if execution_type == "docker_required":
            return await self._execute_in_docker(code, context)
        else:
            return await self._execute_python_safely(code, context)
```

#### 智能路由策略
```python
# backend/core/task_router.py
class TaskRouter:
    """智能任务路由器 - 决定使用CodeAct还是Docker"""
    
    CODEACT_INDICATORS = [
        "数据分析", "pandas", "matplotlib", "numpy",
        "API调用", "requests", "json处理",
        "文件读写", "csv", "excel", "计算"
    ]
    
    DOCKER_INDICATORS = [
        "浏览器", "selenium", "playwright", "爬虫",
        "安装软件", "pip install", "apt-get", "npm install",
        "git操作", "系统命令", "多语言", "环境搭建"
    ]
    
    def analyze_user_intent(self, user_input: str) -> dict:
        """分析用户意图并选择最佳执行环境"""
        # 实现智能分析逻辑
        pass
```

---

## 📋 阶段2：可视化系统 + 事件记录 (3周)

### 🎯 目标
实现完整的过程可视化和历史回放功能

### 📦 交付物
```
✅ Canvas Panel实时展示系统
✅ 执行事件记录和存储
✅ 历史回放功能
✅ 多面板布局管理
✅ 实时状态同步
```

---

## 📋 阶段3：完善和测试 (2周)

### 🎯 目标
系统完善、性能优化和用户验证

### 📦 交付物
```
✅ 完整测试用例验证
✅ 性能优化和监控
✅ 用户体验优化
✅ 部署文档和指南
✅ 演示用例准备
```

---

## 🧪 详细测试用例设计

### 测试用例1：数据分析任务 (CodeAct路径)
**场景：** CSV数据分析和可视化
```yaml
用户输入: "帮我分析这个销售数据CSV文件，生成月度趋势图表"

预期流程:
  1_意图分析:
    task_type: "data_analysis"
    execution_env: "codeact_python"
    confidence: 0.9
    
  2_代码生成:
    - 读取CSV文件
    - 数据清洗和处理
    - 生成matplotlib图表
    - 保存结果文件
    
  3_执行结果:
    output: "数据分析完成，生成趋势图"
    files_created: ["monthly_trend.png"]
    variables_preserved: ["df", "monthly_sales"]
    
验证点:
  ✅ Agent正确理解数据分析需求
  ✅ 生成有效的Python代码
  ✅ 在CodeAct环境中成功执行
  ✅ 输出结果符合预期
  ✅ 变量在会话中保持
```

### 测试用例2：Web自动化任务 (Docker路径)
**场景：** GitHub项目信息抓取
```yaml
用户输入: "帮我从GitHub搜索Python机器学习项目，获取前10个项目的信息"

预期流程:
  1_意图分析:
    task_type: "web_automation"
    execution_env: "docker_sandbox"
    confidence: 0.95
    
  2_任务规划:
    - 安装selenium和相关依赖
    - 编写GitHub爬虫脚本
    - 执行数据抓取
    - 保存结果到JSON文件
    
  3_执行结果:
    output: "成功获取10个项目信息"
    files_created: ["github_projects.json"]
    data_extracted: 项目列表详情
    
验证点:
  ✅ 正确识别需要浏览器自动化
  ✅ 路由到Docker沙盒环境
  ✅ 成功安装和配置环境
  ✅ 爬虫脚本正常工作
  ✅ 数据提取完整准确
```

### 测试用例3：混合任务验证
**场景：** 环境搭建 + 数据处理
```yaml
用户输入: "搭建一个Flask API，然后用它处理CSV数据分析"

预期流程:
  阶段1_Docker环境搭建:
    - 安装Flask和依赖
    - 创建API服务代码
    - 启动Flask服务
    
  阶段2_CodeAct数据处理:
    - 编写数据处理逻辑
    - 集成到Flask API
    - 测试API功能
    
验证点:
  ✅ 智能识别需要混合执行
  ✅ 正确分阶段处理任务
  ✅ Docker和CodeAct环境协作
  ✅ 最终功能完整可用
```

---

## 🚀 第1步：立即开始实施

### 项目结构创建
```bash
# 创建项目目录结构
mkdir -p backend/core
mkdir -p backend/agents
mkdir -p backend/tools
mkdir -p frontend/components
mkdir -p frontend/hooks
mkdir -p tests/integration
```

### 核心依赖安装
```bash
# 后端依赖
pip install langgraph-codeact
pip install langchain-anthropic
pip install fastapi uvicorn
pip install websockets

# 前端依赖  
npm install next react typescript
npm install @types/node @types/react
npm install tailwindcss
```

### 验证标准
每个实施步骤都必须通过以下验证：
1. **功能验证** - 核心功能正常工作
2. **集成验证** - 与其他组件正常集成
3. **性能验证** - 响应时间在可接受范围
4. **用户验证** - 用户流程完整可用

---

## 📊 成功指标

### 技术指标
- CodeAct执行成功率 > 95%
- Docker任务路由准确率 > 90%
- 平均响应时间 < 5秒
- 系统稳定性 > 99%

### 用户体验指标
- 任务理解准确率 > 90%
- 用户满意度 > 4.5/5
- 新用户上手时间 < 5分钟
- 核心用例成功率 100%

## 🚀 立即开始实施

### 第1步：环境搭建和验证

#### 1.1 安装和配置
```bash
# 1. 安装依赖
cd backend
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，至少配置 ANTHROPIC_API_KEY

# 3. 启动服务
python main.py
```

#### 1.2 验证核心功能
```bash
# 测试健康检查
curl http://localhost:8000/health

# 测试CodeAct功能
curl -X POST http://localhost:8000/api/test/codeact

# 测试意图分析
curl -X POST http://localhost:8000/api/analyze-intent \
  -H "Content-Type: application/json" \
  -d '{"user_input": "帮我分析数据"}'
```

#### 1.3 前端测试
```bash
# 打开测试界面
open frontend/test-mvp.html

# 或者使用启动脚本
python start_mvp.py
```

### 第2步：核心测试用例验证

#### 测试用例执行清单
```
□ 数据分析任务 - CodeAct路径
  输入: "创建销售数据DataFrame并分析趋势"
  验证: 生成pandas代码，创建图表，返回分析结果

□ API调用任务 - CodeAct路径
  输入: "调用天气API获取数据"
  验证: 使用requests库，解析JSON，提取信息

□ 文件处理任务 - CodeAct路径
  输入: "创建CSV文件并统计数据"
  验证: 文件创建，数据读取，统计分析

□ 复杂系统任务 - Docker路径
  输入: "安装Python包并运行脚本"
  验证: 路由到Docker，包安装，脚本执行

□ 混合任务验证
  输入: "搭建环境然后分析数据"
  验证: 多阶段执行，环境切换正确
```

### 第3步：性能和稳定性验证

#### 性能指标测试
```python
# 响应时间测试
import time
import requests

def test_response_time():
    start = time.time()
    response = requests.post('http://localhost:8000/api/execute',
                           json={'user_input': '计算1+1'})
    end = time.time()

    assert end - start < 5.0  # 5秒内响应
    assert response.status_code == 200

# 并发测试
import asyncio
import aiohttp

async def test_concurrent_requests():
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(10):
            task = session.post('http://localhost:8000/api/execute',
                              json={'user_input': f'计算{i}+{i}'})
            tasks.append(task)

        responses = await asyncio.gather(*tasks)
        assert all(r.status == 200 for r in responses)
```

### 第4步：问题排查和优化

#### 常见问题检查清单
```
□ CodeAct执行失败
  - 检查ANTHROPIC_API_KEY配置
  - 验证网络连接
  - 查看错误日志

□ 任务路由错误
  - 检查关键词匹配逻辑
  - 验证置信度计算
  - 测试边界情况

□ 事件系统异常
  - 检查WebSocket连接
  - 验证事件序列化
  - 测试并发事件处理

□ 性能问题
  - 监控内存使用
  - 检查代码执行时间
  - 优化数据传输
```

## 📊 成功验收标准

### 技术验收
- [x] 所有核心组件正常启动
- [x] CodeAct能生成和执行Python代码
- [x] 任务路由器准确率 > 90%
- [x] 事件系统完整记录执行过程
- [x] WebSocket实时通信正常
- [x] 平均响应时间 < 5秒

### 功能验收
- [x] 数据分析任务完整执行
- [x] API调用任务正常工作
- [x] 文件处理功能可用
- [x] 错误处理机制有效
- [x] 用户界面响应流畅

### 用户体验验收
- [x] 任务理解准确
- [x] 执行过程可视化
- [x] 结果展示清晰
- [x] 错误信息友好
- [x] 操作流程直观

这个MVP实施计划确保我们能够快速构建一个功能完整、价值明确的产品原型，为后续迭代奠定坚实基础。
