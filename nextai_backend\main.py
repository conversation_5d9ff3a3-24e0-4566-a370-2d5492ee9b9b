"""
NextAI MVP 主应用程序
轻量级智能Agent系统 - 立即可用版本
"""

import asyncio
import logging
import os
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="NextAI MVP - 统一智能体平台",
    description="轻量级智能Agent系统，支持代码生成和执行",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
execution_engine = None
active_websockets: Dict[str, WebSocket] = {}


class TaskRequest(BaseModel):
    """任务请求模型"""
    user_input: str
    session_id: Optional[str] = None
    options: Dict[str, Any] = {}


class TaskResponse(BaseModel):
    """任务响应模型"""
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    session_id: str
    execution_time: float


class MockExecutionEngine:
    """模拟执行引擎 - 演示NextAI核心功能"""
    
    def __init__(self):
        self.task_count = 0
        self.success_count = 0
        logger.info("🎭 初始化模拟执行引擎（演示模式）")
    
    async def process_request(self, user_input: str, session_id: str = None) -> dict:
        """模拟处理用户请求 - 展示智能路由和代码生成"""
        self.task_count += 1
        await asyncio.sleep(0.8)  # 模拟AI思考时间
        
        # 智能任务分析
        if "数据" in user_input or "分析" in user_input or "CSV" in user_input:
            self.success_count += 1
            return {
                "success": True,
                "result": {
                    "type": "data_analysis",
                    "execution_env": "codeact_python",
                    "code": """import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 创建示例销售数据
np.random.seed(42)
dates = pd.date_range('2024-01-01', periods=12, freq='M')
sales_data = {
    'date': dates,
    'product_A': np.random.randint(1000, 5000, 12),
    'product_B': np.random.randint(800, 4000, 12),
    'region': ['北区', '南区', '东区', '西区'] * 3
}

df = pd.DataFrame(sales_data)
df['total_sales'] = df['product_A'] + df['product_B']

# 数据分析
print("📊 销售数据分析报告")
print("=" * 40)
print(f"数据期间: {df['date'].min().strftime('%Y-%m')} 到 {df['date'].max().strftime('%Y-%m')}")
print(f"总销售额: ¥{df['total_sales'].sum():,}")
print(f"平均月销售额: ¥{df['total_sales'].mean():,.0f}")
print(f"最高月销售额: ¥{df['total_sales'].max():,}")

# 生成趋势图
plt.figure(figsize=(12, 6))
plt.plot(df['date'], df['total_sales'], marker='o', linewidth=2)
plt.title('月度销售趋势', fontsize=16)
plt.xlabel('月份')
plt.ylabel('销售额 (¥)')
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('sales_trend.png', dpi=300)
print("\\n📈 趋势图已保存: sales_trend.png")""",
                    "output": """📊 销售数据分析报告
========================================
数据期间: 2024-01 到 2024-12
总销售额: ¥372,847
平均月销售额: ¥31,071
最高月销售额: ¥42,156

📈 趋势图已保存: sales_trend.png""",
                    "files_created": ["sales_trend.png"],
                    "variables": ["df", "sales_data", "dates"],
                    "reasoning": "检测到数据分析需求，使用pandas进行数据处理和matplotlib生成可视化"
                },
                "intent": {
                    "task_type": "data_analysis",
                    "confidence": 0.95,
                    "execution_env": "codeact_python",
                    "tools_used": ["pandas", "matplotlib", "numpy"]
                }
            }
            
        elif "API" in user_input or "请求" in user_input or "接口" in user_input:
            self.success_count += 1
            return {
                "success": True,
                "result": {
                    "type": "api_integration",
                    "execution_env": "codeact_python",
                    "code": """import requests
import json
from datetime import datetime

# GitHub API示例
def get_repo_info(repo_name):
    url = f"https://api.github.com/repos/{repo_name}"
    headers = {'Accept': 'application/vnd.github.v3+json'}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        return {"error": str(e)}

# 获取热门仓库信息
repos = ["microsoft/vscode", "facebook/react", "tensorflow/tensorflow"]
results = []

print("🌐 GitHub仓库信息查询")
print("=" * 50)

for repo in repos:
    print(f"\\n📦 查询仓库: {repo}")
    data = get_repo_info(repo)
    
    if "error" not in data:
        info = {
            "name": data["name"],
            "description": data["description"][:100] + "..." if len(data["description"]) > 100 else data["description"],
            "stars": data["stargazers_count"],
            "language": data["language"],
            "updated": data["updated_at"][:10]
        }
        results.append(info)
        
        print(f"  ⭐ Stars: {info['stars']:,}")
        print(f"  💻 Language: {info['language']}")
        print(f"  📅 Updated: {info['updated']}")
    else:
        print(f"  ❌ Error: {data['error']}")

print(f"\\n✅ 成功查询 {len(results)} 个仓库")""",
                    "output": """🌐 GitHub仓库信息查询
==================================================

📦 查询仓库: microsoft/vscode
  ⭐ Stars: 162,847
  💻 Language: TypeScript
  📅 Updated: 2024-01-15

📦 查询仓库: facebook/react
  ⭐ Stars: 227,234
  💻 Language: JavaScript
  📅 Updated: 2024-01-14

📦 查询仓库: tensorflow/tensorflow
  ⭐ Stars: 185,156
  💻 Language: C++
  📅 Updated: 2024-01-15

✅ 成功查询 3 个仓库""",
                    "files_created": [],
                    "variables": ["repos", "results", "get_repo_info"],
                    "reasoning": "检测到API调用需求，使用requests库进行HTTP请求处理"
                },
                "intent": {
                    "task_type": "api_calls",
                    "confidence": 0.88,
                    "execution_env": "codeact_python",
                    "tools_used": ["requests", "json"]
                }
            }
            
        elif "浏览器" in user_input or "爬虫" in user_input or "网页" in user_input or "selenium" in user_input:
            self.success_count += 1
            return {
                "success": True,
                "result": {
                    "type": "web_automation",
                    "execution_env": "docker_sandbox",
                    "code": """# 🐳 Docker沙盒环境执行
# 安装依赖: pip install selenium beautifulsoup4
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

# 配置Chrome选项
chrome_options = Options()
chrome_options.add_argument('--headless')  # 无头模式
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')

print("🌐 启动浏览器自动化任务...")

# 启动浏览器
driver = webdriver.Chrome(options=chrome_options)

try:
    # 访问示例网站
    print("📱 访问目标网站...")
    driver.get("https://example.com")
    
    # 获取页面信息
    title = driver.title
    print(f"📄 页面标题: {title}")
    
    # 查找页面元素
    heading = driver.find_element(By.TAG_NAME, "h1").text
    print(f"🎯 主标题: {heading}")
    
    # 获取页面链接
    links = driver.find_elements(By.TAG_NAME, "a")
    print(f"🔗 找到 {len(links)} 个链接")
    
    for i, link in enumerate(links[:3]):  # 只显示前3个
        href = link.get_attribute("href")
        text = link.text.strip()
        if href and text:
            print(f"  {i+1}. {text} -> {href}")
    
    print("✅ 网页自动化任务完成")
    
finally:
    driver.quit()
    print("🔒 浏览器已关闭")""",
                    "output": """🌐 启动浏览器自动化任务...
📱 访问目标网站...
📄 页面标题: Example Domain
🎯 主标题: Example Domain
🔗 找到 1 个链接
  1. More information... -> https://www.iana.org/domains/example
✅ 网页自动化任务完成
🔒 浏览器已关闭""",
                    "files_created": [],
                    "variables": ["driver", "title", "heading", "links"],
                    "reasoning": "检测到浏览器自动化需求，路由到Docker沙盒环境执行selenium任务"
                },
                "intent": {
                    "task_type": "web_automation",
                    "confidence": 0.92,
                    "execution_env": "docker_sandbox",
                    "tools_used": ["selenium", "chrome_webdriver"]
                }
            }
            
        elif "计算" in user_input or "公式" in user_input or "数学" in user_input:
            self.success_count += 1
            return {
                "success": True,
                "result": {
                    "type": "calculation",
                    "execution_env": "codeact_python",
                    "code": """import math
import numpy as np
from datetime import datetime

def compound_interest(principal, rate, time, compound_freq=12):
    \"\"\"计算复合利息\"\"\"
    amount = principal * (1 + rate/compound_freq) ** (compound_freq * time)
    interest = amount - principal
    return amount, interest

def investment_analysis(principal, annual_rate, years):
    \"\"\"投资分析\"\"\"
    print("💰 投资复合利息计算")
    print("=" * 40)
    print(f"本金: ¥{principal:,}")
    print(f"年利率: {annual_rate*100:.1f}%")
    print(f"投资期限: {years}年")
    print()
    
    # 不同复利频率的比较
    frequencies = {
        "年复利": 1,
        "半年复利": 2, 
        "季度复利": 4,
        "月复利": 12,
        "日复利": 365
    }
    
    results = []
    for name, freq in frequencies.items():
        final_amount, interest = compound_interest(principal, annual_rate, years, freq)
        results.append((name, final_amount, interest))
        print(f"{name:8}: 最终金额 ¥{final_amount:,.2f}, 利息 ¥{interest:,.2f}")
    
    # 计算最佳收益
    best = max(results, key=lambda x: x[1])
    print(f"\\n🎯 最佳选择: {best[0]}")
    print(f"💎 额外收益: ¥{best[2] - results[0][2]:,.2f}")
    
    return results

# 示例计算
principal = 100000  # 10万本金
annual_rate = 0.05  # 5%年利率
years = 10          # 10年

results = investment_analysis(principal, annual_rate, years)""",
                    "output": """💰 投资复合利息计算
========================================
本金: ¥100,000
年利率: 5.0%
投资期限: 10年

年复利  : 最终金额 ¥162,889.46, 利息 ¥62,889.46
半年复利: 最终金额 ¥163,861.64, 利息 ¥63,861.64
季度复利: 最终金额 ¥164,361.46, 利息 ¥64,361.46
月复利  : 最终金额 ¥164,700.95, 利息 ¥64,700.95
日复利  : 最终金额 ¥164,870.16, 利息 ¥64,870.16

🎯 最佳选择: 日复利
💎 额外收益: ¥1,980.70""",
                    "files_created": [],
                    "variables": ["compound_interest", "investment_analysis", "results"],
                    "reasoning": "检测到数学计算需求，生成复合利息计算和投资分析代码"
                },
                "intent": {
                    "task_type": "calculations",
                    "confidence": 0.85,
                    "execution_env": "codeact_python",
                    "tools_used": ["math", "numpy"]
                }
            }
            
        else:
            # 通用任务处理
            self.success_count += 1
            return {
                "success": True,
                "result": {
                    "type": "general_task",
                    "execution_env": "codeact_python",
                    "code": f"""# NextAI 通用任务处理
import datetime

def process_user_request(request):
    \"\"\"智能处理用户请求\"\"\"
    print("🤖 NextAI正在分析您的请求...")
    print(f"📝 用户输入: {{request}}")
    
    # 模拟智能分析过程
    analysis = {{
        "intent": "通用任务处理",
        "complexity": "中等",
        "estimated_time": "2-3分钟",
        "recommended_approach": "分步骤处理"
    }}
    
    print("\\n🧠 智能分析结果:")
    for key, value in analysis.items():
        print(f"  {key}: {value}")
    
    print("\\n✅ NextAI已准备好协助您完成任务")
    print("💡 提示: 您可以提供更具体的需求以获得更精准的帮助")
    
    return analysis

# 处理当前请求
user_request = "{user_input}"
result = process_user_request(user_request)

print(f"\\n⏰ 处理时间: {{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}}")""",
                    "output": f"""🤖 NextAI正在分析您的请求...
📝 用户输入: {user_input}

🧠 智能分析结果:
  intent: 通用任务处理
  complexity: 中等
  estimated_time: 2-3分钟
  recommended_approach: 分步骤处理

✅ NextAI已准备好协助您完成任务
💡 提示: 您可以提供更具体的需求以获得更精准的帮助

⏰ 处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}""",
                    "files_created": [],
                    "variables": ["process_user_request", "result", "user_request"],
                    "reasoning": "通用任务处理，提供智能分析和建议"
                },
                "intent": {
                    "task_type": "general",
                    "confidence": 0.6,
                    "execution_env": "codeact_python",
                    "tools_used": ["datetime"]
                }
            }
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        return {
            "mode": "demo_simulation",
            "total_tasks": self.task_count,
            "successful_tasks": self.success_count,
            "success_rate": self.success_count / self.task_count if self.task_count > 0 else 1.0,
            "features_demonstrated": [
                "🧠 智能任务理解和路由",
                "⚡ Python代码自动生成",
                "🐳 Docker沙盒环境选择",
                "📊 数据分析和可视化",
                "🌐 API集成和网页自动化",
                "🧮 数学计算和公式处理"
            ],
            "supported_environments": [
                "CodeAct Python执行环境",
                "Docker沙盒安全环境"
            ],
            "core_capabilities": {
                "task_routing": "智能选择最适合的执行环境",
                "code_generation": "基于用户意图生成高质量代码",
                "execution_monitoring": "实时跟踪和记录执行过程",
                "result_visualization": "清晰展示执行结果和文件输出"
            }
        }


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global execution_engine
    
    logger.info("🚀 启动NextAI MVP...")
    
    # 使用演示模式的执行引擎
    execution_engine = MockExecutionEngine()
    
    logger.info("✅ NextAI MVP初始化完成 - 演示模式")


@app.get("/")
async def root():
    """根路径 - 系统概览"""
    return {
        "message": "🚀 NextAI MVP - 统一智能体平台",
        "tagline": "Web端高阶Agent助手，理解意图，选择工具，安全执行",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "core_features": {
            "intelligent_routing": "🧠 智能分析用户需求，自动选择最适合的执行环境",
            "code_generation": "⚡ 基于意图生成高质量Python代码",
            "safe_execution": "🐳 Docker沙盒提供安全的代码执行环境", 
            "real_time_monitoring": "📊 实时监控执行过程，完整记录操作历史",
            "visual_feedback": "🎨 清晰展示代码、输出和生成的文件"
        },
        "demo_scenarios": [
            "📈 数据分析: '帮我分析销售数据并生成趋势图'",
            "🌐 API集成: '调用GitHub API获取仓库信息'", 
            "🕷️ 网页自动化: '用浏览器抓取网站内容'",
            "🧮 数学计算: '计算10万元投资10年的复合利息'"
        ],
        "demo_mode": True
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "execution_engine": execution_engine is not None,
            "engine_type": execution_engine.__class__.__name__ if execution_engine else None,
            "websocket_connections": len(active_websockets)
        },
        "ready_for_demo": True
    }


@app.post("/api/execute", response_model=TaskResponse)
async def execute_task(request: TaskRequest):
    """执行任务 - 核心API"""
    if not execution_engine:
        raise HTTPException(status_code=500, detail="执行引擎未初始化")
    
    start_time = datetime.now()
    session_id = request.session_id or f"session_{int(start_time.timestamp())}"
    
    try:
        logger.info(f"📝 执行任务: {request.user_input[:50]}...")
        
        # 通知WebSocket客户端任务开始
        if session_id in active_websockets:
            await active_websockets[session_id].send_json({
                "type": "task_start",
                "timestamp": start_time.isoformat(),
                "user_input": request.user_input,
                "message": "🤖 NextAI正在分析您的请求..."
            })
        
        # 执行任务
        result = await execution_engine.process_request(
            user_input=request.user_input,
            session_id=session_id
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 通知WebSocket客户端任务完成
        if session_id in active_websockets:
            await active_websockets[session_id].send_json({
                "type": "task_complete",
                "timestamp": datetime.now().isoformat(),
                "execution_time": execution_time,
                "success": result["success"],
                "task_type": result.get("result", {}).get("type", "unknown"),
                "message": "✅ 任务执行完成"
            })
        
        return TaskResponse(
            success=result["success"],
            result=result if result["success"] else None,
            error=result.get("error") if not result["success"] else None,
            session_id=session_id,
            execution_time=execution_time
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"❌ 任务执行失败: {e}")
        
        # 通知WebSocket客户端任务失败
        if session_id in active_websockets:
            await active_websockets[session_id].send_json({
                "type": "task_error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "message": "❌ 任务执行失败"
            })
        
        return TaskResponse(
            success=False,
            error=str(e),
            session_id=session_id,
            execution_time=execution_time
        )


@app.get("/api/stats")
async def get_system_stats():
    """获取系统统计信息"""
    stats = {
        "system": {
            "name": "NextAI MVP",
            "status": "running",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "mode": "demonstration"
        }
    }
    
    if execution_engine:
        stats["execution"] = execution_engine.get_stats()
    
    stats["websockets"] = {
        "active_connections": len(active_websockets),
        "session_ids": list(active_websockets.keys())
    }
    
    return stats


@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket连接 - 实时事件推送"""
    await websocket.accept()
    active_websockets[session_id] = websocket
    
    logger.info(f"🔌 WebSocket连接建立: {session_id}")
    
    try:
        # 发送欢迎消息
        await websocket.send_json({
            "type": "connection_established",
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "message": "🎉 欢迎使用NextAI！实时连接已建立，可以开始体验智能Agent功能"
        })
        
        # 保持连接并处理消息
        while True:
            try:
                message = await websocket.receive_text()
                
                if message == "ping":
                    await websocket.send_text("pong")
                elif message == "status":
                    await websocket.send_json({
                        "type": "status_update",
                        "timestamp": datetime.now().isoformat(),
                        "active_sessions": len(active_websockets),
                        "engine_status": "ready",
                        "message": "🟢 系统运行正常，随时准备处理您的请求"
                    })
                
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"❌ WebSocket错误: {e}")
    finally:
        if session_id in active_websockets:
            del active_websockets[session_id]
        logger.info(f"🔌 WebSocket连接关闭: {session_id}")


if __name__ == "__main__":
    print("🚀 NextAI MVP 启动中...")
    print("=" * 50)
    print("📖 访问 http://localhost:8000 查看系统概览")
    print("📊 访问 http://localhost:8000/docs 查看完整API文档")
    print("🧪 访问前端测试页面开始体验功能")
    print("⚠️  按 Ctrl+C 停止服务")
    print("=" * 50)
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
