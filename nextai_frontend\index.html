<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextAI MVP - 统一智能体平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-badge.connected {
            background: #d1fae5;
            color: #065f46;
        }

        .status-badge.disconnected {
            background: #fee2e2;
            color: #991b1b;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .panel h3 {
            font-size: 1.4em;
            margin-bottom: 20px;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-section {
            margin-bottom: 20px;
        }

        .input-section label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .input-section textarea {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .input-section textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .button:active {
            transform: translateY(0);
        }

        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .output-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .output-section pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #1f2937;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }

        .loading.show {
            display: block;
        }

        .loading .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .test-cases {
            margin-top: 20px;
        }

        .test-case {
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            border: 1px solid #d1d5db;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-case:hover {
            background: linear-gradient(135deg, #e5e7eb, #d1d5db);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .test-case h4 {
            margin-bottom: 8px;
            color: #374151;
            font-size: 16px;
        }

        .test-case p {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
        }

        .events-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .event-item {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 13px;
            transition: background-color 0.2s ease;
        }

        .event-item:hover {
            background-color: #f9fafb;
        }

        .event-item:last-child {
            border-bottom: none;
        }

        .event-type {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 4px;
        }

        .event-time {
            color: #6b7280;
            font-size: 11px;
            margin-bottom: 4px;
        }

        .event-message {
            color: #374151;
            font-size: 12px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            padding: 15px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #0369a1;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
        }

        .demo-banner {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-banner strong {
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 NextAI MVP</h1>
            <p>统一智能体平台 - Web端高阶Agent助手</p>
            <div class="demo-banner">
                <strong>🎭 演示模式</strong> - 展示智能任务理解、代码生成和执行监控功能
            </div>
            <div id="connectionStatus" class="status-badge disconnected">
                🔌 正在连接...
            </div>
        </div>

        <div class="main-content">
            <!-- 左侧：任务执行面板 -->
            <div class="panel">
                <h3>🤖 智能任务执行</h3>
                
                <div class="input-section">
                    <label for="userInput">描述您的需求：</label>
                    <textarea id="userInput" placeholder="例如：帮我分析销售数据并生成趋势图表&#10;或者：调用GitHub API获取热门仓库信息&#10;或者：用浏览器自动化抓取网页内容"></textarea>
                </div>

                <div>
                    <button class="button" onclick="executeTask()" id="executeBtn">
                        ⚡ 执行任务
                    </button>
                    <button class="button secondary" onclick="clearOutput()">
                        🗑️ 清除结果
                    </button>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <span>🤖 NextAI正在处理您的请求...</span>
                </div>

                <div class="output-section">
                    <h4>📋 执行结果：</h4>
                    <pre id="output">等待任务执行...</pre>
                </div>

                <!-- 快速测试用例 -->
                <div class="test-cases">
                    <h4>🧪 快速体验：</h4>
                    
                    <div class="test-case" onclick="loadTestCase('data_analysis')">
                        <h4>📊 数据分析演示</h4>
                        <p>创建销售数据，进行统计分析并生成可视化图表</p>
                    </div>
                    
                    <div class="test-case" onclick="loadTestCase('api_call')">
                        <h4>🌐 API集成演示</h4>
                        <p>调用GitHub API获取热门仓库信息和统计数据</p>
                    </div>
                    
                    <div class="test-case" onclick="loadTestCase('web_automation')">
                        <h4>🕷️ 网页自动化演示</h4>
                        <p>使用浏览器自动化技术抓取网页内容</p>
                    </div>
                    
                    <div class="test-case" onclick="loadTestCase('calculation')">
                        <h4>🧮 数学计算演示</h4>
                        <p>复合利息计算和投资收益分析</p>
                    </div>
                </div>
            </div>

            <!-- 右侧：监控面板 -->
            <div class="panel">
                <h3>📊 实时监控</h3>
                
                <div>
                    <button class="button secondary" onclick="getStats()">
                        📈 获取统计
                    </button>
                    <button class="button secondary" onclick="clearEvents()">
                        🗑️ 清除事件
                    </button>
                </div>

                <div class="stats-grid" id="statsGrid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalTasks">0</div>
                        <div class="stat-label">总任务数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="successRate">100%</div>
                        <div class="stat-label">成功率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="activeConnections">0</div>
                        <div class="stat-label">活跃连接</div>
                    </div>
                </div>

                <h4 style="margin-top: 20px; margin-bottom: 10px;">🔔 实时事件：</h4>
                <div class="events-list" id="eventsList">
                    <div class="event-item">
                        <div class="event-type">系统启动</div>
                        <div class="event-time">等待连接...</div>
                        <div class="event-message">正在建立WebSocket连接</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let currentSessionId = `session_${Date.now()}`;
        let websocket = null;

        // 测试用例
        const testCases = {
            data_analysis: "帮我分析销售数据，创建包含日期、产品、销售额的数据集，然后生成月度趋势图表",
            api_call: "调用GitHub API获取microsoft/vscode、facebook/react、tensorflow/tensorflow这些热门仓库的信息",
            web_automation: "使用浏览器自动化访问example.com网站，获取页面标题和链接信息",
            calculation: "计算10万元本金，年利率5%，投资10年的复合利息，比较不同复利频率的收益差异"
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            connectWebSocket();
            getStats();
        });

        // 检查后端连接
        async function checkConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    updateConnectionStatus('connected', '✅ 系统运行正常');
                } else {
                    updateConnectionStatus('disconnected', '⚠️ 系统状态异常');
                }
            } catch (error) {
                updateConnectionStatus('disconnected', '❌ 无法连接到后端服务');
            }
        }

        // 更新连接状态
        function updateConnectionStatus(type, message) {
            const statusEl = document.getElementById('connectionStatus');
            statusEl.className = `status-badge ${type}`;
            statusEl.textContent = message;
        }

        // 连接WebSocket
        function connectWebSocket() {
            try {
                websocket = new WebSocket(`ws://localhost:8000/ws/${currentSessionId}`);
                
                websocket.onopen = function() {
                    addEvent('WebSocket连接', '实时事件连接已建立', '🔌 可以开始执行任务');
                };
                
                websocket.onmessage = function(event) {
                    const eventData = JSON.parse(event.data);
                    addEvent(
                        eventData.type || 'Unknown', 
                        new Date().toLocaleTimeString(),
                        eventData.message || JSON.stringify(eventData).substring(0, 100)
                    );
                };
                
                websocket.onclose = function() {
                    addEvent('WebSocket断开', '连接已关闭', '🔌 实时事件连接断开');
                };
                
                websocket.onerror = function(error) {
                    addEvent('WebSocket错误', '连接错误', `❌ ${error.message || '连接失败'}`);
                };
            } catch (error) {
                console.error('WebSocket连接失败:', error);
            }
        }

        // 执行任务
        async function executeTask() {
            const userInput = document.getElementById('userInput').value.trim();
            if (!userInput) {
                alert('请输入任务内容');
                return;
            }

            const executeBtn = document.getElementById('executeBtn');
            executeBtn.disabled = true;
            executeBtn.textContent = '🤖 处理中...';
            
            showLoading(true);
            clearOutput();

            try {
                const response = await fetch(`${API_BASE}/api/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_input: userInput,
                        session_id: currentSessionId
                    })
                });

                const result = await response.json();
                displayResult(result);
                
                // 更新统计
                setTimeout(getStats, 500);
                
            } catch (error) {
                displayError(error.message);
            } finally {
                showLoading(false);
                executeBtn.disabled = false;
                executeBtn.textContent = '⚡ 执行任务';
            }
        }

        // 获取统计信息
        async function getStats() {
            try {
                const response = await fetch(`${API_BASE}/api/stats`);
                const stats = await response.json();
                
                // 更新统计卡片
                if (stats.execution) {
                    document.getElementById('totalTasks').textContent = stats.execution.total_tasks || 0;
                    document.getElementById('successRate').textContent = 
                        `${Math.round((stats.execution.success_rate || 1) * 100)}%`;
                }
                
                if (stats.websockets) {
                    document.getElementById('activeConnections').textContent = 
                        stats.websockets.active_connections || 0;
                }
                
            } catch (error) {
                console.error('获取统计失败:', error);
            }
        }

        // 加载测试用例
        function loadTestCase(caseType) {
            document.getElementById('userInput').value = testCases[caseType];
            
            // 添加视觉反馈
            const textarea = document.getElementById('userInput');
            textarea.style.borderColor = '#667eea';
            setTimeout(() => {
                textarea.style.borderColor = '#e5e7eb';
            }, 1000);
        }

        // 显示结果
        function displayResult(result) {
            const output = document.getElementById('output');
            
            if (result.success && result.result) {
                const res = result.result;
                const intent = res.intent || {};
                
                let displayText = `🎯 任务类型: ${intent.task_type || 'unknown'}\n`;
                displayText += `🔧 执行环境: ${intent.execution_env || 'unknown'}\n`;
                displayText += `📊 置信度: ${Math.round((intent.confidence || 0) * 100)}%\n`;
                displayText += `⏱️ 执行时间: ${result.execution_time?.toFixed(2) || 0}秒\n\n`;
                
                if (res.result?.reasoning) {
                    displayText += `🧠 智能分析: ${res.result.reasoning}\n\n`;
                }
                
                if (res.result?.code) {
                    displayText += `📝 生成代码:\n${'-'.repeat(50)}\n${res.result.code}\n${'-'.repeat(50)}\n\n`;
                }
                
                if (res.result?.output) {
                    displayText += `📋 执行输出:\n${res.result.output}\n\n`;
                }
                
                if (res.result?.files_created?.length > 0) {
                    displayText += `📁 创建文件: ${res.result.files_created.join(', ')}\n`;
                }
                
                if (res.result?.variables?.length > 0) {
                    displayText += `🔢 变量: ${res.result.variables.join(', ')}\n`;
                }
                
                output.textContent = displayText;
            } else {
                output.textContent = `❌ 执行失败: ${result.error || '未知错误'}`;
            }
        }

        // 显示错误
        function displayError(error) {
            const output = document.getElementById('output');
            output.textContent = `❌ 错误: ${error}`;
        }

        // 清除输出
        function clearOutput() {
            document.getElementById('output').textContent = '等待任务执行...';
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.getElementById('loading');
            loading.className = show ? 'loading show' : 'loading';
        }

        // 添加事件到列表
        function addEvent(type, time, message) {
            const eventsList = document.getElementById('eventsList');
            const eventItem = document.createElement('div');
            eventItem.className = 'event-item';
            
            const now = typeof time === 'string' ? time : new Date().toLocaleTimeString();
            eventItem.innerHTML = `
                <div class="event-type">${type}</div>
                <div class="event-time">${now}</div>
                <div class="event-message">${message}</div>
            `;
            
            eventsList.insertBefore(eventItem, eventsList.firstChild);
            
            // 限制事件数量
            while (eventsList.children.length > 20) {
                eventsList.removeChild(eventsList.lastChild);
            }
        }

        // 清除事件
        function clearEvents() {
            const eventsList = document.getElementById('eventsList');
            eventsList.innerHTML = `
                <div class="event-item">
                    <div class="event-type">事件已清除</div>
                    <div class="event-time">${new Date().toLocaleTimeString()}</div>
                    <div class="event-message">📝 事件列表已重置</div>
                </div>
            `;
        }
    </script>
</body>
</html>
