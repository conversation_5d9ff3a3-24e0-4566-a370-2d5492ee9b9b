{"name": "@agent-infra/browser", "description": "A tiny Browser Control library, built for Agent Tars.", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepare": "npm run build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest", "test:e2e": "vitest --config vitest.e2e.config.ts", "coverage": "vitest run --coverage", "test:e2e:local": "vitest --config vitest.e2e.config.ts local-browser.e2e.test.ts"}, "dependencies": {"puppeteer-core": "24.1.1", "@agent-infra/logger": "workspace:*", "@agent-infra/shared": "workspace:*"}, "devDependencies": {"@types/node": "20.14.8", "typescript": "^5.7.3", "vitest": "3.0.7", "@vitest/coverage-v8": "3.0.7", "@rslib/core": "0.5.3"}}