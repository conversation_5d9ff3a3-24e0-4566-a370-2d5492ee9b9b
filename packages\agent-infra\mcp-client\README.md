## MCP Client

## Credits

Thanks to:

- [kang<PERSON><PERSON><PERSON>](https://github.com/kangfenmao) for creating a great AI chatbot product [Cherry Studio](https://github.com/CherryHQ/cherry-studio) from which we draw a lot of inspiration for browser detection functionality.
- The [@modelcontextprotocol/sdk](https://github.com/modelcontextprotocol/typescript-sdk) project which helps us develop and use the agent tools better.
