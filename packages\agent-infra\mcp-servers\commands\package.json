{"name": "@agent-infra/mcp-server-commands", "version": "0.0.1", "description": "An MCP server to run arbitrary commands", "type": "module", "main": "./dist/server.cjs", "module": "./dist/server.js", "types": "./dist/server.d.ts", "bin": {"mcp-server-commands": "./dist/index.cjs"}, "files": ["dist"], "scripts": {"clean": "shx rm -rf build", "build": "shx rm -rf build && rslib build && shx chmod +x dist/*.{js,cjs}", "prepare": "npm run build", "watch": "npm run build && rslib build --watch", "dev": "npx -y @modelcontextprotocol/inspector tsx src/index.ts", "inspector": "npx @modelcontextprotocol/inspector build/index.js", "test": "vitest run", "test:watch": "vitest --watch", "test:integration": "vitest tests/integration"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"tsx": "^4.19.3", "@rslib/core": "0.5.3", "zod": "^3.23.8", "shx": "^0.3.4", "vitest": "^3.0.7", "@types/node": "^20.11.24", "typescript": "^5.7.3"}}