{"name": "@agent-infra/mcp-server-filesystem", "version": "0.0.1", "description": "MCP server for filesystem access", "license": "MIT", "type": "module", "main": "./dist/server.cjs", "module": "./dist/server.js", "types": "./dist/server.d.ts", "bin": {"mcp-server-filesystem": "dist/index.cjs"}, "files": ["dist"], "scripts": {"build": "shx rm -rf dist && rslib build && shx chmod +x dist/*.js", "dev": "npx -y @modelcontextprotocol/inspector tsx src/index.ts", "prepare": "npm run build", "watch": "rslib build --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.6.1"}, "devDependencies": {"diff": "^5.1.0", "glob": "^10.3.10", "minimatch": "^10.0.1", "zod-to-json-schema": "^3.23.5", "zod": "^3.23.8", "tsx": "^4.19.3", "vitest": "^3.0.7", "@types/diff": "^5.0.9", "@types/minimatch": "^5.1.2", "@rslib/core": "0.5.3", "@types/node": "^22", "shx": "^0.3.4", "typescript": "^5.7.3"}}