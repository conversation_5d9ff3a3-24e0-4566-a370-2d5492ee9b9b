{"name": "@agent-infra/search", "description": "Isomorphic search client", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepare": "npm run build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest", "test:e2e": "vitest --config vitest.e2e.config.ts", "coverage": "vitest run --coverage", "run:example": "ts-node scripts/example.ts"}, "dependencies": {"@tavily/core": "0.3.1", "@agent-infra/browser-search": "workspace:*", "@agent-infra/bing-search": "workspace:*", "@agent-infra/duckduckgo-search": "workspace:*", "@agent-infra/logger": "workspace:*"}, "devDependencies": {"@agent-infra/browser": "workspace:*", "@types/node": "20.14.8", "rimraf": "4.1.0", "typescript": "4.9.4", "vitest": "3.0.7", "@vitest/coverage-v8": "3.0.7", "ts-node": "10.9.2", "@rslib/core": "0.5.3"}}