[project]
name = "langgraph-codeact"
version = "0.1.3"
description = "LangGraph implementation of CodeAct agent that generates and executes code instead of tool calling."
authors = [
    {name = "<PERSON>uno <PERSON>",email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "langgraph>=0.4.5"
]

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[dependency-groups]
dev = [
    "langchain-anthropic>=0.3.9,<0.4.0",
    "langchain>=0.3.20,<0.4.0",
    "langchain-sandbox>=0.0.5,<0.1.0"
]
test = [
    "pytest>=8.0.0",
    "ruff>=0.9.4",
    "mypy>=1.8.0",
    "pytest-socket>=0.7.0",
    "types-setuptools>=69.0.0",
]

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q -v"
testpaths = [
    "tests",
]
python_files = ["test_*.py"]
python_functions = ["test_*"]

[tool.ruff]
line-length = 100
target-version = "py310"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
]
ignore = [
  "E501" # line-length
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

