#!/usr/bin/env python3
"""
MVP 启动脚本
自动检查环境、安装依赖并启动服务
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path


def print_banner():
    """打印启动横幅"""
    banner = """
🚀 统一智能体平台 MVP
================================
CodeAct + Docker 混合执行引擎
================================
"""
    print(banner)


def check_python_version():
    """检查Python版本"""
    print("📋 检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True


def check_and_install_dependencies():
    """检查并安装依赖"""
    print("\n📦 检查依赖...")

    backend_dir = Path("nextai_backend")
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ 错误: 找不到requirements.txt文件")
        return False
    
    try:
        # 检查是否需要安装依赖
        print("   检查已安装的包...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "list"
        ], capture_output=True, text=True, cwd=backend_dir)
        
        installed_packages = result.stdout.lower()
        
        # 检查关键依赖
        key_packages = ["fastapi", "langchain", "langgraph"]
        missing_packages = []
        
        for package in key_packages:
            if package not in installed_packages:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"   缺少关键依赖: {', '.join(missing_packages)}")
            print("   正在安装依赖...")
            
            # 安装依赖
            install_result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], cwd=backend_dir)
            
            if install_result.returncode != 0:
                print("❌ 依赖安装失败")
                return False
            
            print("✅ 依赖安装完成")
        else:
            print("✅ 依赖已满足")
        
        return True
        
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        return False


def check_environment_variables():
    """检查环境变量"""
    print("\n🔧 检查环境配置...")

    backend_dir = Path("nextai_backend")
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists():
        if env_example.exists():
            print("   创建.env文件...")
            try:
                # 复制示例文件
                with open(env_example, 'r') as f:
                    content = f.read()
                
                with open(env_file, 'w') as f:
                    f.write(content)
                
                print("✅ 已创建.env文件")
                print("⚠️  请编辑backend/.env文件，添加必要的API密钥")
            except Exception as e:
                print(f"❌ 创建.env文件失败: {e}")
                return False
        else:
            print("⚠️  未找到.env文件，将使用默认配置")
    
    # 检查关键环境变量
    required_vars = ["ANTHROPIC_API_KEY"]
    missing_vars = []
    
    # 加载.env文件
    if env_file.exists():
        try:
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        if value and value != "your_api_key_here":
                            os.environ[key] = value
        except Exception as e:
            print(f"⚠️  读取.env文件失败: {e}")
    
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("   系统将使用模拟模式运行")
    else:
        print("✅ 环境配置完成")
    
    return True


def start_backend_server():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")

    backend_dir = Path("nextai_backend")
    main_file = backend_dir / "main.py"
    
    if not main_file.exists():
        print("❌ 错误: 找不到main.py文件")
        return None
    
    try:
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], cwd=backend_dir)
        
        # 等待服务启动
        print("   等待服务启动...")
        time.sleep(3)
        
        # 检查服务是否正在运行
        if process.poll() is None:
            print("✅ 后端服务已启动 (PID: {})".format(process.pid))
            print("   服务地址: http://localhost:8000")
            return process
        else:
            print("❌ 后端服务启动失败")
            return None
            
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return None


def open_test_interface():
    """打开测试界面"""
    print("\n🌐 打开测试界面...")

    frontend_dir = Path("nextai_frontend")
    test_file = frontend_dir / "index.html"
    
    if not test_file.exists():
        print("❌ 错误: 找不到测试界面文件")
        return False
    
    try:
        # 获取绝对路径
        test_url = f"file://{test_file.absolute()}"
        
        # 打开浏览器
        webbrowser.open(test_url)
        print(f"✅ 测试界面已打开: {test_url}")
        return True
        
    except Exception as e:
        print(f"❌ 打开测试界面失败: {e}")
        print(f"   请手动打开: {test_file.absolute()}")
        return False


def print_usage_instructions():
    """打印使用说明"""
    instructions = """
📖 使用说明:
================================

1. 测试界面已在浏览器中打开
2. 在"用户输入"框中输入任务需求
3. 点击"执行任务"按钮开始执行
4. 查看右侧的实时事件监控

🧪 快速测试用例:
- 数据分析: "创建销售数据并分析趋势"
- API调用: "获取天气数据"
- 文件处理: "创建CSV文件并统计"
- 数学计算: "计算复合利息"

🔧 API接口:
- 健康检查: http://localhost:8000/health
- API文档: http://localhost:8000/docs
- 执行任务: POST http://localhost:8000/api/execute

⚠️  注意事项:
- 确保已配置ANTHROPIC_API_KEY环境变量
- 首次运行可能需要下载模型
- 如遇问题请查看控制台日志

按 Ctrl+C 停止服务
================================
"""
    print(instructions)


def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("\n❌ 依赖安装失败，请手动安装:")
        print("   cd nextai_backend && pip install -r requirements.txt")
        sys.exit(1)
    
    # 检查环境变量
    if not check_environment_variables():
        sys.exit(1)
    
    # 启动后端服务
    backend_process = start_backend_server()
    if not backend_process:
        sys.exit(1)
    
    # 打开测试界面
    open_test_interface()
    
    # 打印使用说明
    print_usage_instructions()
    
    try:
        # 等待用户中断
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        backend_process.terminate()
        
        # 等待进程结束
        try:
            backend_process.wait(timeout=5)
            print("✅ 服务已停止")
        except subprocess.TimeoutExpired:
            print("⚠️  强制终止服务")
            backend_process.kill()
    
    print("👋 感谢使用统一智能体平台 MVP!")


if __name__ == "__main__":
    main()
