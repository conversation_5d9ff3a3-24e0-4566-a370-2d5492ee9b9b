# 🏗️ 统一智能体平台 - 技术方案

## 📐 总体架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Web 前端 (Next.js)                        │
├─────────────────┬─────────────────┬─────────────────────────┤
│   聊天界面       │   Canvas Panel   │      终端面板           │
│  (DeerFlow UI)  │  (Agent TARS)   │     (Suna Terminal)     │
└─────────────────┴─────────────────┴─────────────────────────┘
                           │
                    WebSocket/HTTP API
                           │
┌─────────────────────────────────────────────────────────────┐
│                  后端服务 (FastAPI)                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│   研究引擎       │   可视化服务     │     执行引擎             │
│  (DeerFlow)     │  (Event Stream) │   (Docker Sandbox)     │
└─────────────────┴─────────────────┴─────────────────────────┘
                           │
                    云端基础设施
                           │
┌─────────────────┬─────────────────┬─────────────────────────┐
│   Daytona       │   Supabase      │      Redis              │
│   沙盒服务       │   数据库         │      缓存               │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 核心设计原则
- **模块化设计**：各组件独立开发，松耦合集成
- **微服务架构**：服务拆分，独立部署和扩展
- **事件驱动**：基于事件流的实时通信
- **云原生**：容器化部署，支持水平扩展

## 🎨 前端架构

### 技术栈选择
```typescript
// 核心框架
Framework: "Next.js 14"
Language: "TypeScript"
Styling: "Tailwind CSS"
StateManagement: "Zustand"

// UI 组件
Terminal: "xterm.js"
Editor: "Monaco Editor"
Charts: "Recharts"
Animation: "Framer Motion"

// 通信
HTTP: "Axios"
WebSocket: "native WebSocket API"
```

### 组件架构
```typescript
// 主应用组件
interface UnifiedApp {
  layout: {
    sidebar: SidebarComponent;
    mainArea: MainAreaComponent;
    canvasPanel: CanvasPanelComponent;
    statusBar: StatusBarComponent;
  };
  
  state: {
    global: GlobalState;
    research: ResearchState;
    execution: ExecutionState;
    visualization: VisualizationState;
  };
}

// Canvas Panel 组件 (移植自 Agent TARS)
interface CanvasPanelComponent {
  eventPlayer: EventPlayerComponent;
  terminalPanel: TerminalPanelComponent;
  browserPanel: BrowserPanelComponent;
  fileSystemPanel: FileSystemPanelComponent;
}
```

### 状态管理设计
```typescript
// 使用 Zustand 的状态管理
interface AppState {
  // 研究状态 (DeerFlow)
  research: {
    currentPlan: ResearchPlan;
    activeAgent: string;
    progress: number;
    results: ResearchResult[];
  };
  
  // 可视化状态 (Agent TARS)
  visualization: {
    events: EventItem[];
    activePanel: string;
    canvasLayout: LayoutConfig;
  };
  
  // 执行状态 (Suna)
  execution: {
    activeSessions: TerminalSession[];
    dockerContainers: ContainerInfo[];
    fileSystem: FileSystemState;
  };
}
```

## ⚙️ 后端架构

### 服务拆分设计
```python
# 主要服务模块
services = {
    "research_service": "DeerFlow 研究引擎",
    "execution_service": "Suna 执行引擎", 
    "visualization_service": "事件流和可视化",
    "auth_service": "用户认证和授权",
    "file_service": "文件管理服务",
    "notification_service": "实时通知服务"
}
```

### API 设计
```python
# FastAPI 路由设计
from fastapi import FastAPI, WebSocket

app = FastAPI(title="Unified Agent Platform API")

# 研究相关 API (基于 DeerFlow)
@app.post("/api/research/start")
async def start_research(request: ResearchRequest):
    """启动研究任务"""
    pass

@app.post("/api/research/feedback") 
async def provide_feedback(feedback: UserFeedback):
    """提供用户反馈"""
    pass

# 执行相关 API (基于 Suna)
@app.post("/api/execution/command")
async def execute_command(command: CommandRequest):
    """执行命令"""
    pass

@app.websocket("/ws/terminal/{session_id}")
async def terminal_websocket(websocket: WebSocket, session_id: str):
    """终端 WebSocket 连接"""
    pass

# 可视化相关 API (基于 Agent TARS)
@app.websocket("/ws/events")
async def events_websocket(websocket: WebSocket):
    """事件流 WebSocket"""
    pass
```

### 智能体工作流集成
```python
# 统一的智能体调度器
class UnifiedAgentOrchestrator:
    def __init__(self):
        self.deerflow_engine = DeerFlowEngine()
        self.suna_executor = SunaExecutor()
        self.event_manager = EventManager()
    
    async def execute_research_task(self, task: ResearchTask):
        # 1. DeerFlow 制定研究计划
        plan = await self.deerflow_engine.create_plan(task)
        
        # 2. 发送事件到前端可视化
        await self.event_manager.emit("plan_created", plan)
        
        # 3. 执行研究步骤
        for step in plan.steps:
            if step.type == "code_execution":
                # 使用 Suna 执行代码
                result = await self.suna_executor.execute(step)
            elif step.type == "web_search":
                # 使用 DeerFlow 搜索
                result = await self.deerflow_engine.search(step)
            
            # 4. 实时更新进度
            await self.event_manager.emit("step_completed", {
                "step": step,
                "result": result
            })
        
        # 5. 生成最终报告
        report = await self.deerflow_engine.generate_report(plan)
        return report
```

## 🐳 执行环境架构

### Docker 沙盒设计
```yaml
# 基于 Suna 的 Docker 配置
version: '3.8'
services:
  agent-sandbox:
    image: unified-agent/sandbox:latest
    environment:
      - WORKSPACE_PATH=/workspace
      - CHROME_DEBUGGING_PORT=9222
      - VNC_PASSWORD=${VNC_PASSWORD}
    resources:
      limits:
        cpus: '2'
        memory: 4G
      reservations:
        cpus: '1'
        memory: 2G
    volumes:
      - workspace_data:/workspace
      - /var/run/docker.sock:/var/run/docker.sock
```

### 沙盒管理系统
```python
# 沙盒生命周期管理
class SandboxManager:
    def __init__(self):
        self.daytona_client = DaytonaClient()
        self.active_sandboxes = {}
    
    async def create_sandbox(self, user_id: str, project_id: str):
        """创建用户专属沙盒"""
        sandbox = await self.daytona_client.create({
            "image": "unified-agent/sandbox:latest",
            "resources": {"cpu": 2, "memory": 4, "disk": 5},
            "labels": {"user_id": user_id, "project_id": project_id}
        })
        
        self.active_sandboxes[f"{user_id}:{project_id}"] = sandbox
        return sandbox
    
    async def execute_command(self, sandbox_id: str, command: str):
        """在沙盒中执行命令"""
        return await self.daytona_client.execute(sandbox_id, command)
```

## 🔄 实时通信架构

### WebSocket 事件系统
```typescript
// 前端事件监听
class EventManager {
  private ws: WebSocket;
  
  constructor() {
    this.ws = new WebSocket('ws://localhost:8000/ws/events');
    this.setupEventHandlers();
  }
  
  private setupEventHandlers() {
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case 'research_progress':
          this.updateResearchProgress(data.payload);
          break;
        case 'command_output':
          this.updateTerminalOutput(data.payload);
          break;
        case 'file_changed':
          this.updateFileSystem(data.payload);
          break;
      }
    };
  }
}
```

### 事件流设计
```python
# 后端事件发布系统
class EventPublisher:
    def __init__(self):
        self.connections: List[WebSocket] = []
    
    async def subscribe(self, websocket: WebSocket):
        """订阅事件流"""
        self.connections.append(websocket)
    
    async def publish(self, event_type: str, payload: dict):
        """发布事件到所有订阅者"""
        event = {
            "type": event_type,
            "payload": payload,
            "timestamp": datetime.now().isoformat()
        }
        
        for connection in self.connections:
            try:
                await connection.send_json(event)
            except ConnectionClosed:
                self.connections.remove(connection)
```

## 💾 数据架构

### 数据库设计
```sql
-- 用户和项目管理
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    name VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE projects (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    name VARCHAR(255),
    description TEXT,
    config JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 研究任务和结果
CREATE TABLE research_tasks (
    id UUID PRIMARY KEY,
    project_id UUID REFERENCES projects(id),
    query TEXT,
    plan JSONB,
    status VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 执行历史
CREATE TABLE execution_history (
    id UUID PRIMARY KEY,
    task_id UUID REFERENCES research_tasks(id),
    command TEXT,
    output TEXT,
    exit_code INTEGER,
    executed_at TIMESTAMP DEFAULT NOW()
);

-- 事件日志
CREATE TABLE event_logs (
    id UUID PRIMARY KEY,
    task_id UUID REFERENCES research_tasks(id),
    event_type VARCHAR(100),
    payload JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 缓存策略
```python
# Redis 缓存设计
class CacheManager:
    def __init__(self):
        self.redis = Redis(host='localhost', port=6379)
    
    async def cache_research_result(self, query_hash: str, result: dict):
        """缓存研究结果"""
        await self.redis.setex(
            f"research:{query_hash}", 
            3600,  # 1小时过期
            json.dumps(result)
        )
    
    async def cache_sandbox_state(self, sandbox_id: str, state: dict):
        """缓存沙盒状态"""
        await self.redis.setex(
            f"sandbox:{sandbox_id}",
            1800,  # 30分钟过期
            json.dumps(state)
        )
```

## 🔐 安全架构

### 认证和授权
```python
# JWT 认证系统
from fastapi_users import FastAPIUsers
from fastapi_users.authentication import JWTAuthentication

# 用户认证配置
jwt_authentication = JWTAuthentication(
    secret=SECRET_KEY,
    lifetime_seconds=3600,
    tokenUrl="auth/jwt/login",
)

# 权限控制
class PermissionChecker:
    @staticmethod
    def check_project_access(user: User, project_id: str):
        """检查用户是否有项目访问权限"""
        return user.id == project.user_id or user.is_admin
    
    @staticmethod  
    def check_sandbox_access(user: User, sandbox_id: str):
        """检查用户是否有沙盒访问权限"""
        sandbox = get_sandbox(sandbox_id)
        return sandbox.user_id == user.id
```

### 沙盒安全
```python
# Docker 安全配置
DOCKER_SECURITY_CONFIG = {
    "user": "appuser",  # 非 root 用户
    "read_only": True,  # 只读文件系统
    "no_new_privileges": True,  # 禁止提权
    "security_opt": ["no-new-privileges:true"],
    "cap_drop": ["ALL"],  # 移除所有权限
    "cap_add": ["CHOWN", "SETUID", "SETGID"],  # 只添加必要权限
}
```

## 📊 监控和日志

### 监控系统
```python
# 性能监控
from prometheus_client import Counter, Histogram, Gauge

# 指标定义
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests')
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
ACTIVE_USERS = Gauge('active_users_total', 'Number of active users')
SANDBOX_COUNT = Gauge('active_sandboxes_total', 'Number of active sandboxes')

# 健康检查
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "database": await check_database(),
            "redis": await check_redis(),
            "daytona": await check_daytona()
        }
    }
```

### 日志系统
```python
# 结构化日志
import structlog

logger = structlog.get_logger()

# 操作日志
async def log_user_action(user_id: str, action: str, details: dict):
    logger.info(
        "user_action",
        user_id=user_id,
        action=action,
        details=details,
        timestamp=datetime.now().isoformat()
    )
```

## 🚀 部署架构

### 容器化部署
```dockerfile
# 前端 Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

```dockerfile
# 后端 Dockerfile  
FROM python:3.12-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Kubernetes 部署
```yaml
# k8s 部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: unified-agent-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: unified-agent-backend
  template:
    metadata:
      labels:
        app: unified-agent-backend
    spec:
      containers:
      - name: backend
        image: unified-agent/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

## 📈 性能优化

### 前端优化
- **代码分割**：按路由和组件分割
- **懒加载**：非关键组件延迟加载
- **缓存策略**：静态资源和 API 响应缓存
- **CDN 加速**：静态资源 CDN 分发

### 后端优化
- **连接池**：数据库连接池优化
- **异步处理**：I/O 密集型操作异步化
- **缓存层**：Redis 缓存热点数据
- **负载均衡**：多实例负载分发

### 数据库优化
- **索引优化**：关键查询字段建索引
- **分区表**：大表按时间分区
- **读写分离**：读操作分离到从库
- **查询优化**：SQL 查询性能调优

## 🔄 开发流程和规范

### 开发环境搭建
```bash
# 1. 克隆项目
git clone https://github.com/your-org/unified-agent-platform.git
cd unified-agent-platform

# 2. 安装前端依赖
cd frontend
npm install

# 3. 安装后端依赖
cd ../backend
pip install -r requirements.txt

# 4. 启动开发环境
docker-compose -f docker-compose.dev.yml up -d
npm run dev  # 前端
uvicorn main:app --reload  # 后端
```

### 代码规范
```typescript
// TypeScript 代码规范
interface ComponentProps {
  title: string;
  onAction: (data: ActionData) => void;
}

// 使用 ESLint + Prettier
const config = {
  extends: ['@typescript-eslint/recommended'],
  rules: {
    'no-console': 'warn',
    'prefer-const': 'error'
  }
};
```

```python
# Python 代码规范
from typing import Optional, Dict, Any
import asyncio

class ServiceBase:
    """服务基类"""

    def __init__(self, config: Dict[str, Any]) -> None:
        self.config = config

    async def process(self, data: Optional[str] = None) -> Dict[str, Any]:
        """处理数据"""
        # 使用 Black + isort + mypy
        pass
```

### Git 工作流
```bash
# 功能分支开发
git checkout -b feature/canvas-panel-integration
git add .
git commit -m "feat: integrate Agent TARS canvas panel"
git push origin feature/canvas-panel-integration

# 提交规范 (Conventional Commits)
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建/工具相关
```

## 🚀 部署和运维

### CI/CD 流水线
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Tests
        run: |
          npm test
          pytest

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build Docker Images
        run: |
          docker build -t unified-agent/frontend .
          docker build -t unified-agent/backend .

      - name: Push to Registry
        run: |
          docker push unified-agent/frontend:latest
          docker push unified-agent/backend:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f k8s/
          kubectl rollout status deployment/unified-agent
```

### 监控和告警
```python
# 监控配置
monitoring_config = {
    "metrics": {
        "prometheus": "http://prometheus:9090",
        "grafana": "http://grafana:3000"
    },
    "logging": {
        "elasticsearch": "http://elasticsearch:9200",
        "kibana": "http://kibana:5601"
    },
    "alerting": {
        "slack_webhook": "https://hooks.slack.com/...",
        "email_smtp": "smtp.gmail.com:587"
    }
}

# 告警规则
alerts = [
    {
        "name": "high_cpu_usage",
        "condition": "cpu_usage > 80%",
        "duration": "5m",
        "action": "send_slack_alert"
    },
    {
        "name": "high_error_rate",
        "condition": "error_rate > 5%",
        "duration": "2m",
        "action": "send_email_alert"
    }
]
```

## 📚 技术选型说明

### 前端技术选型
| 技术 | 选择 | 理由 |
|------|------|------|
| 框架 | Next.js 14 | SSR支持、性能优化、生态完善 |
| 语言 | TypeScript | 类型安全、开发效率、维护性 |
| 状态管理 | Zustand | 轻量级、简单易用、性能好 |
| 样式 | Tailwind CSS | 原子化CSS、开发效率高 |
| 终端 | xterm.js | 功能完善、性能好、社区活跃 |

### 后端技术选型
| 技术 | 选择 | 理由 |
|------|------|------|
| 框架 | FastAPI | 高性能、自动文档、类型提示 |
| 语言 | Python 3.12 | 生态丰富、AI库支持好 |
| 数据库 | PostgreSQL | 功能强大、JSON支持、可靠性 |
| 缓存 | Redis | 高性能、数据结构丰富 |
| 消息队列 | RabbitMQ | 可靠性高、功能完善 |

### 基础设施选型
| 组件 | 选择 | 理由 |
|------|------|------|
| 容器化 | Docker | 标准化、可移植性 |
| 编排 | Kubernetes | 自动化运维、扩展性 |
| 沙盒 | Daytona | 专业AI沙盒、安全性 |
| 监控 | Prometheus + Grafana | 开源、功能强大 |
| 日志 | ELK Stack | 成熟方案、查询能力强 |
